#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
手动分割数据集质量分析工具
检查标注质量、类别分布、数据一致性等问题
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any
import glob
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ManualDatasetAnalyzer:
    """手动数据集分析器"""

    def __init__(self, dataset_path: str = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007"):
        self.dataset_path = dataset_path
        self.image_dir = os.path.join(dataset_path, "JPEGImages")
        self.label_dir = os.path.join(dataset_path, "SegmentationClass")
        self.num_classes = 29

        # 分析结果存储
        self.analysis_results = {}

    def check_dataset_structure(self) -> Dict[str, Any]:
        """检查数据集结构"""
        logger.info("🔍 检查数据集结构...")

        structure_info = {
            'dataset_path': self.dataset_path,
            'image_dir_exists': os.path.exists(self.image_dir),
            'label_dir_exists': os.path.exists(self.label_dir),
            'issues': []
        }

        if not structure_info['image_dir_exists']:
            structure_info['issues'].append(f"图像目录不存在: {self.image_dir}")

        if not structure_info['label_dir_exists']:
            structure_info['issues'].append(f"标签目录不存在: {self.label_dir}")

        if structure_info['image_dir_exists']:
            image_files = glob.glob(os.path.join(self.image_dir, "*.jpg")) + \
                         glob.glob(os.path.join(self.image_dir, "*.png"))
            structure_info['image_count'] = len(image_files)
            structure_info['image_extensions'] = list(set([os.path.splitext(f)[1] for f in image_files]))
        else:
            structure_info['image_count'] = 0
            structure_info['image_extensions'] = []

        if structure_info['label_dir_exists']:
            label_files = glob.glob(os.path.join(self.label_dir, "*.png"))
            structure_info['label_count'] = len(label_files)
        else:
            structure_info['label_count'] = 0

        logger.info(f"✅ 结构检查完成: {structure_info['image_count']} 图像, {structure_info['label_count']} 标签")
        return structure_info

    def check_file_correspondence(self) -> Dict[str, Any]:
        """检查文件对应关系"""
        logger.info("🔍 检查文件对应关系...")

        correspondence_info = {
            'matched_pairs': 0,
            'missing_labels': [],
            'missing_images': [],
            'orphaned_labels': [],
            'orphaned_images': []
        }

        if not (os.path.exists(self.image_dir) and os.path.exists(self.label_dir)):
            correspondence_info['error'] = "图像或标签目录不存在"
            return correspondence_info

        # 获取所有文件
        image_files = glob.glob(os.path.join(self.image_dir, "*.jpg")) + \
                     glob.glob(os.path.join(self.image_dir, "*.png"))
        label_files = glob.glob(os.path.join(self.label_dir, "*.png"))

        # 提取基础文件名
        image_basenames = {os.path.splitext(os.path.basename(f))[0] for f in image_files}
        label_basenames = {os.path.splitext(os.path.basename(f))[0] for f in label_files}

        # 检查对应关系
        correspondence_info['matched_pairs'] = len(image_basenames & label_basenames)
        correspondence_info['missing_labels'] = list(image_basenames - label_basenames)
        correspondence_info['missing_images'] = list(label_basenames - image_basenames)
        correspondence_info['orphaned_images'] = correspondence_info['missing_labels']
        correspondence_info['orphaned_labels'] = correspondence_info['missing_images']

        logger.info(f"✅ 对应关系检查: {correspondence_info['matched_pairs']} 配对, "
                   f"{len(correspondence_info['missing_labels'])} 缺失标签, "
                   f"{len(correspondence_info['missing_images'])} 缺失图像")

        return correspondence_info

    def analyze_class_distribution(self) -> Dict[str, Any]:
        """分析类别分布"""
        logger.info("🔍 分析类别分布...")

        class_pixel_counts = np.zeros(self.num_classes, dtype=np.int64)
        class_image_counts = np.zeros(self.num_classes, dtype=np.int64)
        total_pixels = 0
        processed_images = 0

        label_files = glob.glob(os.path.join(self.label_dir, "*.png"))

        for label_file in label_files:
            try:
                label = cv2.imread(label_file, cv2.IMREAD_GRAYSCALE)
                if label is None:
                    continue

                processed_images += 1
                total_pixels += label.size

                # 统计每个类别的像素数
                unique_classes, counts = np.unique(label, return_counts=True)

                for class_id, count in zip(unique_classes, counts):
                    if 0 <= class_id < self.num_classes:
                        class_pixel_counts[class_id] += count
                        class_image_counts[class_id] += 1

            except Exception as e:
                logger.warning(f"处理标签文件失败: {label_file}, 错误: {e}")

        # 计算分布统计
        class_pixel_ratios = class_pixel_counts / max(total_pixels, 1)
        class_image_ratios = class_image_counts / max(processed_images, 1)

        distribution_info = {
            'total_images': processed_images,
            'total_pixels': int(total_pixels),
            'class_pixel_counts': class_pixel_counts.tolist(),
            'class_image_counts': class_image_counts.tolist(),
            'class_pixel_ratios': class_pixel_ratios.tolist(),
            'class_image_ratios': class_image_ratios.tolist(),
            'empty_classes': [i for i, count in enumerate(class_pixel_counts) if count == 0],
            'rare_classes': [i for i, ratio in enumerate(class_pixel_ratios) if 0 < ratio < 0.001],
            'dominant_classes': [i for i, ratio in enumerate(class_pixel_ratios) if ratio > 0.1]
        }

        logger.info(f"✅ 类别分布分析: {processed_images} 图像, "
                   f"{len(distribution_info['empty_classes'])} 空类别, "
                   f"{len(distribution_info['rare_classes'])} 稀有类别")

        return distribution_info

    def check_annotation_quality(self, sample_size: int = 50) -> Dict[str, Any]:
        """检查标注质量"""
        logger.info(f"🔍 检查标注质量 (采样 {sample_size} 张)...")

        quality_info = {
            'checked_images': 0,
            'invalid_values': [],
            'size_mismatches': [],
            'suspicious_patterns': [],
            'edge_quality_issues': [],
            'noise_issues': []
        }

        label_files = glob.glob(os.path.join(self.label_dir, "*.png"))
        image_files = glob.glob(os.path.join(self.image_dir, "*.jpg")) + \
                     glob.glob(os.path.join(self.image_dir, "*.png"))

        # 创建文件名映射
        image_map = {os.path.splitext(os.path.basename(f))[0]: f for f in image_files}

        # 采样检查
        sample_files = label_files[:min(sample_size, len(label_files))]

        for label_file in sample_files:
            try:
                basename = os.path.splitext(os.path.basename(label_file))[0]

                # 读取标签
                label = cv2.imread(label_file, cv2.IMREAD_GRAYSCALE)
                if label is None:
                    continue

                quality_info['checked_images'] += 1

                # 检查无效值
                unique_values = np.unique(label)
                invalid_vals = [int(v) for v in unique_values if v >= self.num_classes]
                if invalid_vals:
                    quality_info['invalid_values'].append({
                        'file': basename,
                        'invalid_values': invalid_vals
                    })

                # 检查尺寸匹配
                if basename in image_map:
                    image = cv2.imread(image_map[basename])
                    if image is not None:
                        if image.shape[:2] != label.shape:
                            quality_info['size_mismatches'].append({
                                'file': basename,
                                'image_size': image.shape[:2],
                                'label_size': label.shape
                            })

                # 检查可疑模式
                # 1. 过度分散的小区域
                contours, _ = cv2.findContours(
                    (label > 0).astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
                )
                small_regions = sum(1 for c in contours if cv2.contourArea(c) < 100)
                if small_regions > 50:  # 超过50个小区域
                    quality_info['suspicious_patterns'].append({
                        'file': basename,
                        'issue': 'too_many_small_regions',
                        'count': small_regions
                    })

                # 2. 边缘质量检查
                edges = cv2.Canny(label, 1, 1)
                edge_density = np.sum(edges > 0) / label.size
                if edge_density > 0.1:  # 边缘密度过高
                    quality_info['edge_quality_issues'].append({
                        'file': basename,
                        'edge_density': float(edge_density)
                    })

                # 3. 噪声检查
                # 使用形态学操作检测噪声
                kernel = np.ones((3, 3), np.uint8)
                cleaned = cv2.morphologyEx(label, cv2.MORPH_CLOSE, kernel)
                noise_pixels = np.sum(label != cleaned)
                noise_ratio = noise_pixels / label.size
                if noise_ratio > 0.05:  # 噪声比例过高
                    quality_info['noise_issues'].append({
                        'file': basename,
                        'noise_ratio': float(noise_ratio)
                    })

            except Exception as e:
                logger.warning(f"质量检查失败: {label_file}, 错误: {e}")

        logger.info(f"✅ 质量检查完成: {quality_info['checked_images']} 图像, "
                   f"{len(quality_info['invalid_values'])} 无效值, "
                   f"{len(quality_info['size_mismatches'])} 尺寸不匹配")

        return quality_info

    def analyze_image_properties(self, sample_size: int = 100) -> Dict[str, Any]:
        """分析图像属性"""
        logger.info(f"🔍 分析图像属性 (采样 {sample_size} 张)...")

        properties_info = {
            'checked_images': 0,
            'resolutions': [],
            'aspect_ratios': [],
            'brightness_stats': [],
            'contrast_stats': [],
            'color_channels': []
        }

        image_files = glob.glob(os.path.join(self.image_dir, "*.jpg")) + \
                     glob.glob(os.path.join(self.image_dir, "*.png"))

        sample_files = image_files[:min(sample_size, len(image_files))]

        for image_file in sample_files:
            try:
                image = cv2.imread(image_file)
                if image is None:
                    continue

                properties_info['checked_images'] += 1

                # 分辨率
                h, w = image.shape[:2]
                properties_info['resolutions'].append((w, h))
                properties_info['aspect_ratios'].append(w / h)

                # 亮度和对比度
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                properties_info['brightness_stats'].append(float(np.mean(gray)))
                properties_info['contrast_stats'].append(float(np.std(gray)))

                # 颜色通道
                properties_info['color_channels'].append(image.shape[2] if len(image.shape) == 3 else 1)

            except Exception as e:
                logger.warning(f"属性分析失败: {image_file}, 错误: {e}")

        # 计算统计信息
        if properties_info['resolutions']:
            unique_resolutions = list(set(properties_info['resolutions']))
            properties_info['unique_resolutions'] = unique_resolutions
            properties_info['resolution_consistency'] = len(unique_resolutions) == 1

            properties_info['avg_brightness'] = np.mean(properties_info['brightness_stats'])
            properties_info['avg_contrast'] = np.mean(properties_info['contrast_stats'])
            properties_info['aspect_ratio_std'] = np.std(properties_info['aspect_ratios'])

        logger.info(f"✅ 图像属性分析: {properties_info['checked_images']} 图像, "
                   f"{len(properties_info.get('unique_resolutions', []))} 种分辨率")

        return properties_info

    def run_complete_analysis(self) -> Dict[str, Any]:
        """运行完整分析"""
        logger.info("🚀 开始完整数据集质量分析...")

        analysis_results = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'dataset_path': self.dataset_path,
            'structure': self.check_dataset_structure(),
            'correspondence': self.check_file_correspondence(),
            'class_distribution': self.analyze_class_distribution(),
            'annotation_quality': self.check_annotation_quality(),
            'image_properties': self.analyze_image_properties()
        }

        # 生成问题总结
        analysis_results['issues_summary'] = self._summarize_issues(analysis_results)

        self.analysis_results = analysis_results
        logger.info("✅ 完整分析完成")

        return analysis_results

    def _summarize_issues(self, results: Dict[str, Any]) -> Dict[str, List[str]]:
        """总结发现的问题"""
        issues = {
            'critical': [],
            'major': [],
            'minor': [],
            'suggestions': []
        }

        # 结构问题
        if not results['structure']['image_dir_exists']:
            issues['critical'].append("图像目录不存在")
        if not results['structure']['label_dir_exists']:
            issues['critical'].append("标签目录不存在")

        # 对应关系问题
        if results['correspondence'].get('missing_labels'):
            count = len(results['correspondence']['missing_labels'])
            if count > 10:
                issues['major'].append(f"大量图像缺少标签: {count} 个")
            else:
                issues['minor'].append(f"部分图像缺少标签: {count} 个")

        if results['correspondence'].get('missing_images'):
            count = len(results['correspondence']['missing_images'])
            issues['minor'].append(f"孤立标签文件: {count} 个")

        # 类别分布问题
        dist = results['class_distribution']
        if dist.get('empty_classes'):
            count = len(dist['empty_classes'])
            issues['major'].append(f"空类别: {count} 个类别没有样本")

        if dist.get('rare_classes'):
            count = len(dist['rare_classes'])
            issues['major'].append(f"稀有类别: {count} 个类别样本极少")

        if dist.get('dominant_classes'):
            count = len(dist['dominant_classes'])
            if count > 3:
                issues['major'].append(f"类别不平衡严重: {count} 个主导类别")

        # 标注质量问题
        quality = results['annotation_quality']
        if quality.get('invalid_values'):
            count = len(quality['invalid_values'])
            issues['critical'].append(f"无效标注值: {count} 个文件")

        if quality.get('size_mismatches'):
            count = len(quality['size_mismatches'])
            issues['major'].append(f"尺寸不匹配: {count} 个文件")

        if quality.get('noise_issues'):
            count = len(quality['noise_issues'])
            issues['minor'].append(f"标注噪声: {count} 个文件")

        # 图像属性问题
        props = results['image_properties']
        if not props.get('resolution_consistency', True):
            issues['minor'].append("图像分辨率不一致")

        if props.get('aspect_ratio_std', 0) > 0.5:
            issues['minor'].append("图像宽高比差异较大")

        # 建议
        if dist.get('rare_classes'):
            issues['suggestions'].append("考虑为稀有类别增加数据增强")

        if quality.get('edge_quality_issues'):
            issues['suggestions'].append("考虑改进边缘标注质量")

        if not props.get('resolution_consistency', True):
            issues['suggestions'].append("建议统一图像分辨率")

        return issues

    def generate_report(self, output_path: str = None) -> str:
        """生成分析报告"""
        if not self.analysis_results:
            self.run_complete_analysis()

        if output_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f'manual_dataset_analysis_{timestamp}.md'

        report = self._generate_markdown_report()

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)

        # 同时保存JSON格式的详细数据
        json_path = output_path.replace('.md', '.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"分析报告已保存: {output_path}")
        logger.info(f"详细数据已保存: {json_path}")

        return report

    def _generate_markdown_report(self) -> str:
        """生成Markdown格式的报告"""
        results = self.analysis_results

        report = f"""# 📊 手动分割数据集质量分析报告

## 📋 基本信息

**分析时间**: {results['timestamp']}
**数据集路径**: {results['dataset_path']}
**分析工具**: 手动数据集质量分析器

---

## 🏗️ 数据集结构

### 目录结构
- **图像目录**: {'✅ 存在' if results['structure']['image_dir_exists'] else '❌ 不存在'}
- **标签目录**: {'✅ 存在' if results['structure']['label_dir_exists'] else '❌ 不存在'}
- **图像数量**: {results['structure'].get('image_count', 0)}
- **标签数量**: {results['structure'].get('label_count', 0)}
- **图像格式**: {', '.join(results['structure'].get('image_extensions', []))}

### 文件对应关系
- **匹配对数**: {results['correspondence'].get('matched_pairs', 0)}
- **缺失标签**: {len(results['correspondence'].get('missing_labels', []))} 个
- **孤立标签**: {len(results['correspondence'].get('orphaned_labels', []))} 个

---

## 📈 类别分布分析

### 总体统计
- **处理图像数**: {results['class_distribution'].get('total_images', 0)}
- **总像素数**: {results['class_distribution'].get('total_pixels', 0):,}
- **空类别数**: {len(results['class_distribution'].get('empty_classes', []))}
- **稀有类别数**: {len(results['class_distribution'].get('rare_classes', []))}
- **主导类别数**: {len(results['class_distribution'].get('dominant_classes', []))}

### 类别分布详情
"""

        # 类别分布表格
        if results['class_distribution'].get('class_pixel_counts'):
            pixel_counts = results['class_distribution']['class_pixel_counts']
            pixel_ratios = results['class_distribution']['class_pixel_ratios']
            image_counts = results['class_distribution']['class_image_counts']

            report += "\n| 类别ID | 像素数 | 像素比例 | 图像数 | 状态 |\n"
            report += "|--------|--------|----------|--------|------|\n"

            for i in range(min(len(pixel_counts), 29)):
                status = "🔴 空" if pixel_counts[i] == 0 else \
                        "🟡 稀有" if pixel_ratios[i] < 0.001 else \
                        "🟢 正常" if pixel_ratios[i] < 0.1 else "🔵 主导"

                report += f"| {i:2d} | {pixel_counts[i]:,} | {pixel_ratios[i]:.4f} | {image_counts[i]} | {status} |\n"

        report += f"""
---

## 🎯 标注质量分析

### 质量检查结果
- **检查图像数**: {results['annotation_quality'].get('checked_images', 0)}
- **无效值问题**: {len(results['annotation_quality'].get('invalid_values', []))} 个文件
- **尺寸不匹配**: {len(results['annotation_quality'].get('size_mismatches', []))} 个文件
- **可疑模式**: {len(results['annotation_quality'].get('suspicious_patterns', []))} 个文件
- **边缘质量问题**: {len(results['annotation_quality'].get('edge_quality_issues', []))} 个文件
- **噪声问题**: {len(results['annotation_quality'].get('noise_issues', []))} 个文件

"""

        # 详细问题列表
        quality = results['annotation_quality']
        if quality.get('invalid_values'):
            report += "### ❌ 无效标注值问题\n"
            for issue in quality['invalid_values'][:5]:  # 只显示前5个
                report += f"- **{issue['file']}**: 无效值 {issue['invalid_values']}\n"
            if len(quality['invalid_values']) > 5:
                report += f"- ... 还有 {len(quality['invalid_values']) - 5} 个文件\n"
            report += "\n"

        if quality.get('size_mismatches'):
            report += "### ⚠️ 尺寸不匹配问题\n"
            for issue in quality['size_mismatches'][:3]:
                report += f"- **{issue['file']}**: 图像{issue['image_size']} vs 标签{issue['label_size']}\n"
            if len(quality['size_mismatches']) > 3:
                report += f"- ... 还有 {len(quality['size_mismatches']) - 3} 个文件\n"
            report += "\n"

        report += f"""---

## 🖼️ 图像属性分析

### 基本属性
- **检查图像数**: {results['image_properties'].get('checked_images', 0)}
- **分辨率一致性**: {'✅ 一致' if results['image_properties'].get('resolution_consistency', False) else '❌ 不一致'}
- **平均亮度**: {results['image_properties'].get('avg_brightness', 0):.1f}
- **平均对比度**: {results['image_properties'].get('avg_contrast', 0):.1f}
- **宽高比标准差**: {results['image_properties'].get('aspect_ratio_std', 0):.3f}

"""

        if results['image_properties'].get('unique_resolutions'):
            report += "### 图像分辨率分布\n"
            resolutions = results['image_properties']['unique_resolutions']
            for res in resolutions[:10]:  # 只显示前10种
                report += f"- {res[0]}×{res[1]}\n"
            if len(resolutions) > 10:
                report += f"- ... 还有 {len(resolutions) - 10} 种分辨率\n"
            report += "\n"

        report += """---

## 🚨 问题总结

"""

        issues = results['issues_summary']

        if issues.get('critical'):
            report += "### 🔴 严重问题 (需要立即修复)\n"
            for issue in issues['critical']:
                report += f"- {issue}\n"
            report += "\n"

        if issues.get('major'):
            report += "### 🟡 主要问题 (建议修复)\n"
            for issue in issues['major']:
                report += f"- {issue}\n"
            report += "\n"

        if issues.get('minor'):
            report += "### 🟢 次要问题 (可选修复)\n"
            for issue in issues['minor']:
                report += f"- {issue}\n"
            report += "\n"

        if issues.get('suggestions'):
            report += "### 💡 改进建议\n"
            for suggestion in issues['suggestions']:
                report += f"- {suggestion}\n"
            report += "\n"

        report += """---

## 🎯 修复建议

### 立即行动
1. **修复严重问题**: 处理无效标注值、缺失文件等
2. **统一数据格式**: 确保图像和标签尺寸匹配
3. **验证标注正确性**: 人工检查可疑标注

### 数据质量改进
1. **类别平衡**: 为稀有类别增加样本或数据增强
2. **标注精度**: 改进边缘标注质量，减少噪声
3. **数据一致性**: 统一图像分辨率和格式

### 训练策略调整
1. **权重平衡**: 使用类别权重处理不平衡问题
2. **损失函数**: 考虑使用Focal Loss处理稀有类别
3. **数据增强**: 针对性增强稀有类别样本

---

## 📊 数据集评分

"""

        # 计算评分
        score = self._calculate_dataset_score(results)

        report += f"""
**总体评分**: {score['total']:.1f}/10.0

- **结构完整性**: {score['structure']:.1f}/2.0
- **文件一致性**: {score['correspondence']:.1f}/2.0
- **类别平衡性**: {score['balance']:.1f}/2.0
- **标注质量**: {score['quality']:.1f}/2.0
- **图像一致性**: {score['consistency']:.1f}/2.0

### 评分说明
- **8.0-10.0**: 优秀，可直接用于训练
- **6.0-7.9**: 良好，需要少量修复
- **4.0-5.9**: 一般，需要较多改进
- **0.0-3.9**: 较差，需要大量修复

---

*报告生成时间: {results['timestamp']}*
"""

        return report

    def _calculate_dataset_score(self, results: Dict[str, Any]) -> Dict[str, float]:
        """计算数据集质量评分"""
        scores = {}

        # 结构完整性 (2分)
        structure_score = 0
        if results['structure']['image_dir_exists']:
            structure_score += 1
        if results['structure']['label_dir_exists']:
            structure_score += 1
        scores['structure'] = structure_score

        # 文件一致性 (2分)
        correspondence = results['correspondence']
        total_files = correspondence.get('matched_pairs', 0) + \
                     len(correspondence.get('missing_labels', [])) + \
                     len(correspondence.get('missing_images', []))

        if total_files > 0:
            match_ratio = correspondence.get('matched_pairs', 0) / total_files
            scores['correspondence'] = match_ratio * 2
        else:
            scores['correspondence'] = 0

        # 类别平衡性 (2分)
        dist = results['class_distribution']
        empty_classes = len(dist.get('empty_classes', []))
        rare_classes = len(dist.get('rare_classes', []))
        total_classes = 29

        balance_score = max(0, 2 - (empty_classes * 0.2) - (rare_classes * 0.1))
        scores['balance'] = min(2, balance_score)

        # 标注质量 (2分)
        quality = results['annotation_quality']
        checked = quality.get('checked_images', 1)
        issues = len(quality.get('invalid_values', [])) + \
                len(quality.get('size_mismatches', [])) + \
                len(quality.get('noise_issues', []))

        issue_ratio = issues / checked
        quality_score = max(0, 2 - issue_ratio * 10)
        scores['quality'] = min(2, quality_score)

        # 图像一致性 (2分)
        props = results['image_properties']
        consistency_score = 2

        if not props.get('resolution_consistency', True):
            consistency_score -= 0.5

        if props.get('aspect_ratio_std', 0) > 0.5:
            consistency_score -= 0.5

        scores['consistency'] = max(0, consistency_score)

        # 总分
        scores['total'] = sum(scores.values())

        return scores

def main():
    """主函数"""
    print("📊 手动分割数据集质量分析")
    print("=" * 60)

    # 检查可能的数据集路径
    possible_paths = [
        "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007",
        "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025",
        "VOCdevkit/VOC2007",
        "VOCdevkit/VOC2025"
    ]

    dataset_path = None
    for path in possible_paths:
        if os.path.exists(path):
            dataset_path = path
            break

    if dataset_path is None:
        print("❌ 未找到数据集，请检查路径")
        print("尝试的路径:")
        for path in possible_paths:
            print(f"  - {path}")
        return

    print(f"✅ 找到数据集: {dataset_path}")

    # 创建分析器
    analyzer = ManualDatasetAnalyzer(dataset_path)

    # 运行分析
    print("\n🔍 开始分析...")
    results = analyzer.run_complete_analysis()

    # 生成报告
    print("\n📝 生成报告...")
    report = analyzer.generate_report()

    # 显示关键结果
    print("\n" + "="*60)
    print("📊 分析结果摘要")
    print("="*60)

    structure = results['structure']
    correspondence = results['correspondence']
    distribution = results['class_distribution']
    quality = results['annotation_quality']

    print(f"📁 数据集结构:")
    print(f"   图像: {structure.get('image_count', 0)} 个")
    print(f"   标签: {structure.get('label_count', 0)} 个")
    print(f"   匹配: {correspondence.get('matched_pairs', 0)} 对")

    print(f"\n📈 类别分布:")
    print(f"   空类别: {len(distribution.get('empty_classes', []))} 个")
    print(f"   稀有类别: {len(distribution.get('rare_classes', []))} 个")
    print(f"   主导类别: {len(distribution.get('dominant_classes', []))} 个")

    print(f"\n🎯 标注质量:")
    print(f"   检查图像: {quality.get('checked_images', 0)} 个")
    print(f"   无效值: {len(quality.get('invalid_values', []))} 个文件")
    print(f"   尺寸不匹配: {len(quality.get('size_mismatches', []))} 个文件")

    # 显示评分
    score = analyzer._calculate_dataset_score(results)
    print(f"\n📊 数据集评分: {score['total']:.1f}/10.0")

    if score['total'] >= 8.0:
        print("🎉 数据集质量优秀！")
    elif score['total'] >= 6.0:
        print("✅ 数据集质量良好，需要少量修复")
    elif score['total'] >= 4.0:
        print("⚠️ 数据集质量一般，需要较多改进")
    else:
        print("❌ 数据集质量较差，需要大量修复")

    # 显示主要问题
    issues = results['issues_summary']
    if issues.get('critical'):
        print(f"\n🔴 严重问题 ({len(issues['critical'])} 个):")
        for issue in issues['critical']:
            print(f"   - {issue}")

    if issues.get('major'):
        print(f"\n🟡 主要问题 ({len(issues['major'])} 个):")
        for issue in issues['major'][:3]:  # 只显示前3个
            print(f"   - {issue}")
        if len(issues['major']) > 3:
            print(f"   - ... 还有 {len(issues['major']) - 3} 个问题")

if __name__ == "__main__":
    main()
