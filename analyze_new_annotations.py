#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新增标注内容分析工具
分析D:\add_90_pictures路径下新增的手动标注图片
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Set
import json
import matplotlib.pyplot as plt
from collections import Counter

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewAnnotationAnalyzer:
    """新增标注内容分析器"""
    
    def __init__(self, annotation_dir: str = r"D:\add_90_pictures"):
        self.annotation_dir = Path(annotation_dir)
        
        # 支持的图像格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 类别标签映射 (基于之前提供的标签列表)
        self.class_labels = {
            0: 'tree', 1: 'grass', 2: 'water body', 3: 'sky', 4: 'mountain',
            5: 'sign board', 6: 'shrub', 7: 'farmland', 8: 'vegetable garden',
            9: 'street lamp', 10: 'artistic wall paintings', 11: 'pole',
            12: 'buildings', 13: 'road', 14: 'historic building',
            15: 'landscape sculpture', 16: 'cultural landscape wall',
            17: 'safety facility', 18: 'sanitation facility', 19: 'service facility',
            20: 'fence', 21: 'pavement', 22: 'tree pit', 23: 'steps',
            24: 'retaining wall', 25: 'peach tree', 26: 'miscellaneous elements',
            27: 'planter'
        }
        
        # 重点关注的稀有类别
        self.target_classes = {
            26: 'miscellaneous elements',  # 类别28 -> 26 (0-based)
            27: 'planter',                 # 类别27 -> 27 (0-based)
            6: 'shrub',                    # 类别8 -> 6 (0-based)
            11: 'pole',                    # 类别17 -> 11 (0-based)
            22: 'tree pit'                 # 类别22 -> 22 (0-based)
        }
        
        # 分析结果
        self.analysis_results = {}
        
    def scan_annotation_directory(self) -> Dict[str, List[Path]]:
        """扫描标注目录，分类文件"""
        if not self.annotation_dir.exists():
            logger.error(f"❌ 标注目录不存在: {self.annotation_dir}")
            return {}
        
        files = {
            'images': [],
            'masks': [],
            'labels': [],
            'others': []
        }
        
        for file_path in self.annotation_dir.iterdir():
            if file_path.is_file():
                suffix = file_path.suffix.lower()
                name = file_path.name.lower()
                
                if suffix in self.image_extensions:
                    if 'mask' in name or 'label' in name or 'gt' in name:
                        files['masks'].append(file_path)
                    else:
                        files['images'].append(file_path)
                elif suffix in {'.txt', '.json', '.xml'}:
                    files['labels'].append(file_path)
                else:
                    files['others'].append(file_path)
        
        logger.info(f"📁 目录扫描结果:")
        logger.info(f"   图像文件: {len(files['images'])} 个")
        logger.info(f"   掩码文件: {len(files['masks'])} 个")
        logger.info(f"   标签文件: {len(files['labels'])} 个")
        logger.info(f"   其他文件: {len(files['others'])} 个")
        
        return files
    
    def analyze_image_properties(self, image_files: List[Path]) -> Dict:
        """分析图像属性"""
        if not image_files:
            return {}
        
        logger.info("🔍 分析图像属性...")
        
        properties = {
            'count': len(image_files),
            'sizes': [],
            'formats': Counter(),
            'file_sizes': [],
            'mean_colors': [],
            'sample_files': []
        }
        
        for i, img_path in enumerate(image_files):
            try:
                # 读取图像
                image = cv2.imread(str(img_path))
                if image is None:
                    logger.warning(f"⚠️ 无法读取图像: {img_path}")
                    continue
                
                # 基本属性
                height, width, channels = image.shape
                properties['sizes'].append((width, height))
                properties['formats'][img_path.suffix.lower()] += 1
                properties['file_sizes'].append(img_path.stat().st_size)
                properties['mean_colors'].append(np.mean(image, axis=(0, 1)))
                
                # 保存前几个文件名作为样本
                if i < 10:
                    properties['sample_files'].append(img_path.name)
                
            except Exception as e:
                logger.warning(f"⚠️ 分析图像失败 {img_path}: {e}")
        
        # 计算统计信息
        if properties['sizes']:
            sizes = np.array(properties['sizes'])
            properties['size_stats'] = {
                'mean_width': np.mean(sizes[:, 0]),
                'mean_height': np.mean(sizes[:, 1]),
                'min_size': tuple(np.min(sizes, axis=0)),
                'max_size': tuple(np.max(sizes, axis=0)),
                'unique_sizes': len(set(map(tuple, sizes)))
            }
        
        if properties['file_sizes']:
            file_sizes = np.array(properties['file_sizes'])
            properties['file_size_stats'] = {
                'mean_mb': np.mean(file_sizes) / (1024 * 1024),
                'total_mb': np.sum(file_sizes) / (1024 * 1024),
                'min_mb': np.min(file_sizes) / (1024 * 1024),
                'max_mb': np.max(file_sizes) / (1024 * 1024)
            }
        
        return properties
    
    def analyze_mask_content(self, mask_files: List[Path]) -> Dict:
        """分析掩码内容，识别包含的类别"""
        if not mask_files:
            return {}
        
        logger.info("🔍 分析掩码内容...")
        
        mask_analysis = {
            'count': len(mask_files),
            'class_distribution': Counter(),
            'class_pixel_counts': Counter(),
            'images_per_class': Counter(),
            'target_class_analysis': {},
            'sample_masks': []
        }
        
        for i, mask_path in enumerate(mask_files):
            try:
                # 读取掩码
                mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
                if mask is None:
                    logger.warning(f"⚠️ 无法读取掩码: {mask_path}")
                    continue
                
                # 分析类别分布
                unique_values, counts = np.unique(mask, return_counts=True)
                
                for class_id, pixel_count in zip(unique_values, counts):
                    if class_id == 0:  # 跳过背景
                        continue
                    
                    # 调整类别ID (如果需要)
                    adjusted_class_id = class_id - 1 if class_id > 0 else class_id
                    
                    mask_analysis['class_distribution'][adjusted_class_id] += 1
                    mask_analysis['class_pixel_counts'][adjusted_class_id] += pixel_count
                    mask_analysis['images_per_class'][adjusted_class_id] += 1
                
                # 保存前几个掩码信息作为样本
                if i < 5:
                    mask_info = {
                        'filename': mask_path.name,
                        'shape': mask.shape,
                        'unique_classes': [int(v-1) if v > 0 else int(v) for v in unique_values if v > 0],
                        'class_names': [self.class_labels.get(int(v-1), f'unknown_{v-1}') if v > 0 else 'background' for v in unique_values]
                    }
                    mask_analysis['sample_masks'].append(mask_info)
                
            except Exception as e:
                logger.warning(f"⚠️ 分析掩码失败 {mask_path}: {e}")
        
        # 分析目标类别
        for class_id, class_name in self.target_classes.items():
            if class_id in mask_analysis['class_distribution']:
                mask_analysis['target_class_analysis'][class_name] = {
                    'class_id': class_id,
                    'image_count': mask_analysis['images_per_class'][class_id],
                    'total_pixels': mask_analysis['class_pixel_counts'][class_id],
                    'avg_pixels_per_image': mask_analysis['class_pixel_counts'][class_id] / mask_analysis['images_per_class'][class_id]
                }
        
        return mask_analysis
    
    def evaluate_annotation_quality(self, image_files: List[Path], mask_files: List[Path]) -> Dict:
        """评估标注质量"""
        quality_metrics = {
            'image_mask_pairs': 0,
            'missing_pairs': [],
            'size_mismatches': [],
            'quality_score': 0.0,
            'recommendations': []
        }
        
        if not image_files or not mask_files:
            quality_metrics['recommendations'].append("缺少图像或掩码文件")
            return quality_metrics
        
        logger.info("🔍 评估标注质量...")
        
        # 检查图像-掩码配对
        image_names = {f.stem for f in image_files}
        mask_names = {f.stem for f in mask_files}
        
        paired_names = image_names & mask_names
        quality_metrics['image_mask_pairs'] = len(paired_names)
        quality_metrics['missing_pairs'] = list((image_names | mask_names) - paired_names)
        
        # 检查尺寸匹配
        for name in list(paired_names)[:10]:  # 检查前10对
            try:
                img_path = next(f for f in image_files if f.stem == name)
                mask_path = next(f for f in mask_files if f.stem == name)
                
                img = cv2.imread(str(img_path))
                mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
                
                if img is not None and mask is not None:
                    if img.shape[:2] != mask.shape:
                        quality_metrics['size_mismatches'].append({
                            'name': name,
                            'image_size': img.shape[:2],
                            'mask_size': mask.shape
                        })
                        
            except Exception as e:
                logger.warning(f"⚠️ 检查配对失败 {name}: {e}")
        
        # 计算质量评分
        total_files = len(image_files) + len(mask_files)
        if total_files > 0:
            pair_ratio = (quality_metrics['image_mask_pairs'] * 2) / total_files
            mismatch_penalty = len(quality_metrics['size_mismatches']) / max(quality_metrics['image_mask_pairs'], 1)
            quality_metrics['quality_score'] = max(0, pair_ratio - mismatch_penalty)
        
        # 生成建议
        if quality_metrics['image_mask_pairs'] == 0:
            quality_metrics['recommendations'].append("❌ 没有找到匹配的图像-掩码对")
        elif quality_metrics['missing_pairs']:
            quality_metrics['recommendations'].append(f"⚠️ 有 {len(quality_metrics['missing_pairs'])} 个文件缺少配对")
        
        if quality_metrics['size_mismatches']:
            quality_metrics['recommendations'].append(f"⚠️ 有 {len(quality_metrics['size_mismatches'])} 对文件尺寸不匹配")
        
        if quality_metrics['quality_score'] > 0.8:
            quality_metrics['recommendations'].append("✅ 标注质量良好")
        elif quality_metrics['quality_score'] > 0.6:
            quality_metrics['recommendations'].append("⚠️ 标注质量中等，建议检查")
        else:
            quality_metrics['recommendations'].append("❌ 标注质量较差，需要修复")
        
        return quality_metrics
    
    def run_complete_analysis(self) -> Dict:
        """运行完整分析"""
        logger.info("🚀 开始分析新增标注内容")
        
        # 1. 扫描目录
        files = self.scan_annotation_directory()
        
        # 2. 分析图像属性
        image_analysis = self.analyze_image_properties(files['images'])
        
        # 3. 分析掩码内容
        mask_analysis = self.analyze_mask_content(files['masks'])
        
        # 4. 评估标注质量
        quality_analysis = self.evaluate_annotation_quality(files['images'], files['masks'])
        
        # 5. 汇总结果
        self.analysis_results = {
            'directory': str(self.annotation_dir),
            'scan_time': datetime.now().isoformat(),
            'file_summary': {
                'images': len(files['images']),
                'masks': len(files['masks']),
                'labels': len(files['labels']),
                'others': len(files['others'])
            },
            'image_analysis': image_analysis,
            'mask_analysis': mask_analysis,
            'quality_analysis': quality_analysis
        }
        
        return self.analysis_results
    
    def generate_report(self) -> str:
        """生成分析报告"""
        if not self.analysis_results:
            return ""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"new_annotation_analysis_{timestamp}.json"
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"📄 详细报告已保存: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"❌ 保存报告失败: {e}")
            return ""

def main():
    """主函数"""
    print("📊 新增标注内容分析工具")
    print("=" * 60)
    
    # 检查目录是否存在
    annotation_dir = r"D:\add_90_pictures"
    
    if not os.path.exists(annotation_dir):
        print(f"❌ 标注目录不存在: {annotation_dir}")
        return
    
    print(f"📁 分析目录: {annotation_dir}")
    
    # 创建分析器
    analyzer = NewAnnotationAnalyzer(annotation_dir)
    
    # 执行分析
    results = analyzer.run_complete_analysis()
    
    # 显示结果摘要
    print(f"\n📊 分析结果摘要:")
    print(f"   图像文件: {results['file_summary']['images']} 个")
    print(f"   掩码文件: {results['file_summary']['masks']} 个")
    print(f"   标签文件: {results['file_summary']['labels']} 个")
    
    # 显示图像分析
    if 'image_analysis' in results and results['image_analysis']:
        img_analysis = results['image_analysis']
        if 'size_stats' in img_analysis:
            print(f"\n📐 图像尺寸统计:")
            print(f"   平均尺寸: {img_analysis['size_stats']['mean_width']:.0f} x {img_analysis['size_stats']['mean_height']:.0f}")
            print(f"   尺寸范围: {img_analysis['size_stats']['min_size']} ~ {img_analysis['size_stats']['max_size']}")
        
        if 'file_size_stats' in img_analysis:
            print(f"\n💾 文件大小统计:")
            print(f"   平均大小: {img_analysis['file_size_stats']['mean_mb']:.2f} MB")
            print(f"   总大小: {img_analysis['file_size_stats']['total_mb']:.2f} MB")
    
    # 显示掩码分析
    if 'mask_analysis' in results and results['mask_analysis']:
        mask_analysis = results['mask_analysis']
        if 'target_class_analysis' in mask_analysis:
            print(f"\n🎯 目标类别分析:")
            for class_name, info in mask_analysis['target_class_analysis'].items():
                print(f"   {class_name}: {info['image_count']} 张图像, {info['total_pixels']} 像素")
        
        if 'sample_masks' in mask_analysis and mask_analysis['sample_masks']:
            print(f"\n📋 掩码样本:")
            for sample in mask_analysis['sample_masks'][:3]:
                print(f"   {sample['filename']}: 包含类别 {sample['unique_classes']}")
    
    # 显示质量分析
    if 'quality_analysis' in results:
        quality = results['quality_analysis']
        print(f"\n✅ 质量评估:")
        print(f"   图像-掩码配对: {quality['image_mask_pairs']} 对")
        print(f"   质量评分: {quality['quality_score']:.2f}")
        
        if quality['recommendations']:
            print(f"   建议:")
            for rec in quality['recommendations']:
                print(f"     {rec}")
    
    # 生成详细报告
    report_path = analyzer.generate_report()
    if report_path:
        print(f"\n📄 详细分析报告: {report_path}")

if __name__ == "__main__":
    main()
