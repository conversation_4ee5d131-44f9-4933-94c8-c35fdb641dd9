{"current_baseline": {"miou": 0.4472, "images": 120}, "thresholds": {"minimal_improvement": {"target_miou": 0.46, "target_improvement": 0.012800000000000034, "required_images": 15, "required_classes": [28], "actual_improvement": 0.0513, "actual_miou": 0.4985, "cost_estimate": 375, "time_estimate": 7.5, "efficiency": 0.00342}, "noticeable_improvement": {"target_miou": 0.47, "target_improvement": 0.022799999999999987, "required_images": 15, "required_classes": [28], "actual_improvement": 0.0513, "actual_miou": 0.4985, "cost_estimate": 375, "time_estimate": 7.5, "efficiency": 0.00342}, "significant_improvement": {"target_miou": 0.49, "target_improvement": 0.042800000000000005, "required_images": 20, "required_classes": [28], "actual_improvement": 0.0513, "actual_miou": 0.4985, "cost_estimate": 500, "time_estimate": 10.0, "efficiency": 0.002565}, "paper_level": {"target_miou": 0.5, "target_improvement": 0.052800000000000014, "required_images": 50, "required_classes": [28, 27], "actual_improvement": 0.06588, "actual_miou": 0.51308, "cost_estimate": 1250, "time_estimate": 25.0, "efficiency": 0.0013176}, "good_performance": {"target_miou": 0.52, "target_improvement": 0.07280000000000003, "required_images": 65, "required_classes": [28, 27, 8], "actual_improvement": 0.073, "actual_miou": 0.5202, "cost_estimate": 1625, "time_estimate": 32.5, "efficiency": 0.001123076923076923}, "excellent_performance": {"target_miou": 0.55, "target_improvement": 0.10280000000000006, "required_images": Infinity, "required_classes": [], "actual_improvement": 0, "actual_miou": 0.4472, "cost_estimate": Infinity, "time_estimate": Infinity, "efficiency": 0.0}, "sota_performance": {"target_miou": 0.6, "target_improvement": 0.1528, "required_images": Infinity, "required_classes": [], "actual_improvement": 0, "actual_miou": 0.4472, "cost_estimate": Infinity, "time_estimate": Infinity, "efficiency": 0.0}}, "incremental_strategy": [{"step": 1, "class_id": 28, "class_images": 20, "cumulative_images": 20, "class_improvement": 0.0513, "cumulative_improvement": 0.0513, "new_miou": 0.4985, "cost": 500, "time_hours": 10.0, "efficiency": 0.002565, "milestone": "显著改进"}, {"step": 2, "class_id": 27, "class_images": 30, "cumulative_images": 50, "class_improvement": 0.01458, "cumulative_improvement": 0.06588, "new_miou": 0.51308, "cost": 1250, "time_hours": 25.0, "efficiency": 0.0013176, "milestone": "论文级别"}, {"step": 3, "class_id": 8, "class_images": 15, "cumulative_images": 65, "class_improvement": 0.0071200000000000005, "cumulative_improvement": 0.073, "new_miou": 0.5202, "cost": 1625, "time_hours": 32.5, "efficiency": 0.001123076923076923, "milestone": "良好性能"}, {"step": 4, "class_id": 17, "class_images": 15, "cumulative_images": 80, "class_improvement": 0.006089999999999999, "cumulative_improvement": 0.07909, "new_miou": 0.5262899999999999, "cost": 2000, "time_hours": 40.0, "efficiency": 0.000988625, "milestone": "良好性能"}, {"step": 5, "class_id": 22, "class_images": 15, "cumulative_images": 95, "class_improvement": 0.00516, "cumulative_improvement": 0.08424999999999999, "new_miou": 0.53145, "cost": 2375, "time_hours": 47.5, "efficiency": 0.0008868421052631578, "milestone": "良好性能"}, {"step": 6, "class_id": 18, "class_images": 12, "cumulative_images": 107, "class_improvement": 0.00325, "cumulative_improvement": 0.0875, "new_miou": 0.5347, "cost": 2675, "time_hours": 53.5, "efficiency": 0.0008177570093457943, "milestone": "良好性能"}, {"step": 7, "class_id": 19, "class_images": 12, "cumulative_images": 119, "class_improvement": 0.0025199999999999997, "cumulative_improvement": 0.09001999999999999, "new_miou": 0.53722, "cost": 2975, "time_hours": 59.5, "efficiency": 0.000756470588235294, "milestone": "良好性能"}, {"step": 8, "class_id": 9, "class_images": 15, "cumulative_images": 134, "class_improvement": 0.0013499999999999999, "cumulative_improvement": 0.09136999999999999, "new_miou": 0.53857, "cost": 3350, "time_hours": 67.0, "efficiency": 0.000681865671641791, "milestone": "良好性能"}, {"step": 9, "class_id": 26, "class_images": 12, "cumulative_images": 146, "class_improvement": 0.0012599999999999998, "cumulative_improvement": 0.09262999999999999, "new_miou": 0.53983, "cost": 3650, "time_hours": 73.0, "efficiency": 0.0006344520547945205, "milestone": "良好性能"}, {"step": 10, "class_id": 15, "class_images": 10, "cumulative_images": 156, "class_improvement": 0.00114, "cumulative_improvement": 0.09376999999999999, "new_miou": 0.54097, "cost": 3900, "time_hours": 78.0, "efficiency": 0.0006010897435897436, "milestone": "良好性能"}, {"step": 11, "class_id": 11, "class_images": 12, "cumulative_images": 168, "class_improvement": 0.00105, "cumulative_improvement": 0.09481999999999999, "new_miou": 0.54202, "cost": 4200, "time_hours": 84.0, "efficiency": 0.0005644047619047618, "milestone": "良好性能"}], "minimum_viable": {"strategy": "minimum_viable", "target_class": 28, "required_images": 15, "expected_improvement": 0.0513, "expected_miou": 0.4985, "cost": 375, "time_hours": 7.5, "roi": 0.1368, "description": "仅补充完全缺失的类别28，获得最大单类别收益"}, "recommendations": {"budget_constrained": {"low_budget": {"budget_range": "0-500元", "recommended_images": 15, "expected_miou": 0.4985, "strategy": "仅补充类别28"}, "medium_budget": {"budget_range": "500-1500元", "recommended_images": 50, "expected_miou": 0.51308, "strategy": "达到论文级别"}, "high_budget": {"budget_range": "1500-3000元", "recommended_images": Infinity, "expected_miou": 0.4472, "strategy": "追求优秀性能"}}, "performance_target": {"paper_submission": {"target_miou": 0.5, "target_improvement": 0.052800000000000014, "required_images": 50, "required_classes": [28, 27], "actual_improvement": 0.06588, "actual_miou": 0.51308, "cost_estimate": 1250, "time_estimate": 25.0, "efficiency": 0.0013176}, "commercial_application": {"target_miou": 0.52, "target_improvement": 0.07280000000000003, "required_images": 65, "required_classes": [28, 27, 8], "actual_improvement": 0.073, "actual_miou": 0.5202, "cost_estimate": 1625, "time_estimate": 32.5, "efficiency": 0.001123076923076923}, "research_benchmark": {"target_miou": 0.55, "target_improvement": 0.10280000000000006, "required_images": Infinity, "required_classes": [], "actual_improvement": 0, "actual_miou": 0.4472, "cost_estimate": Infinity, "time_estimate": Infinity, "efficiency": 0.0}, "sota_competition": {"target_miou": 0.6, "target_improvement": 0.1528, "required_images": Infinity, "required_classes": [], "actual_improvement": 0, "actual_miou": 0.4472, "cost_estimate": Infinity, "time_estimate": Infinity, "efficiency": 0.0}}, "time_constrained": {"urgent": {"time_limit": "1周", "max_images": 20, "strategy": {"step": 1, "class_id": 28, "class_images": 20, "cumulative_images": 20, "class_improvement": 0.0513, "cumulative_improvement": 0.0513, "new_miou": 0.4985, "cost": 500, "time_hours": 10.0, "efficiency": 0.002565, "milestone": "显著改进"}, "description": "紧急情况下的最小改进"}, "normal": {"time_limit": "1个月", "max_images": 80, "strategy": {"step": 4, "class_id": 17, "class_images": 15, "cumulative_images": 80, "class_improvement": 0.006089999999999999, "cumulative_improvement": 0.07909, "new_miou": 0.5262899999999999, "cost": 2000, "time_hours": 40.0, "efficiency": 0.000988625, "milestone": "良好性能"}, "description": "正常时间安排下的平衡方案"}, "relaxed": {"time_limit": "3个月", "max_images": 150, "strategy": {"step": 11, "class_id": 11, "class_images": 12, "cumulative_images": 168, "class_improvement": 0.00105, "cumulative_improvement": 0.09481999999999999, "new_miou": 0.54202, "cost": 4200, "time_hours": 84.0, "efficiency": 0.0005644047619047618, "milestone": "良好性能"}, "description": "充足时间下的完整优化"}}}, "class_priorities": {"28": {"impact": 0.0513, "min_images": 15, "optimal_images": 20}, "27": {"impact": 0.0162, "min_images": 20, "optimal_images": 30}, "8": {"impact": 0.0089, "min_images": 8, "optimal_images": 15}, "17": {"impact": 0.0087, "min_images": 8, "optimal_images": 15}, "22": {"impact": 0.0086, "min_images": 8, "optimal_images": 15}, "18": {"impact": 0.0065, "min_images": 6, "optimal_images": 12}, "19": {"impact": 0.0063, "min_images": 6, "optimal_images": 12}, "9": {"impact": 0.0045, "min_images": 10, "optimal_images": 15}, "26": {"impact": 0.0042, "min_images": 8, "optimal_images": 12}, "15": {"impact": 0.0038, "min_images": 5, "optimal_images": 10}, "11": {"impact": 0.0035, "min_images": 8, "optimal_images": 12}}}