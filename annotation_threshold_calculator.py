#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标注阈值计算器
计算达到不同性能阈值所需的最少标注数量
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import json
from datetime import datetime

class AnnotationThresholdCalculator:
    """标注阈值计算器"""
    
    def __init__(self):
        # 当前数据集基线
        self.current_miou = 0.4472
        self.current_images = 120
        
        # 关键性能阈值
        self.thresholds = {
            'minimal_improvement': 0.46,    # 最小改进 (+3%)
            'noticeable_improvement': 0.47, # 明显改进 (+5%)
            'significant_improvement': 0.49, # 显著改进 (+10%)
            'paper_level': 0.50,            # 论文级别
            'good_performance': 0.52,       # 良好性能
            'excellent_performance': 0.55,  # 优秀性能
            'sota_performance': 0.60        # SOTA性能
        }
        
        # 类别优先级 (基于影响程度)
        self.class_priorities = {
            28: {'impact': 0.0513, 'min_images': 15, 'optimal_images': 20},  # 缺失类别，影响最大
            27: {'impact': 0.0162, 'min_images': 20, 'optimal_images': 30},  # 极稀有
            8:  {'impact': 0.0089, 'min_images': 8, 'optimal_images': 15},   # 严重稀有
            17: {'impact': 0.0087, 'min_images': 8, 'optimal_images': 15},   # 严重稀有
            22: {'impact': 0.0086, 'min_images': 8, 'optimal_images': 15},   # 严重稀有
            18: {'impact': 0.0065, 'min_images': 6, 'optimal_images': 12},   # 中等稀有
            19: {'impact': 0.0063, 'min_images': 6, 'optimal_images': 12},   # 中等稀有
            9:  {'impact': 0.0045, 'min_images': 10, 'optimal_images': 15},  # 中等稀有
            26: {'impact': 0.0042, 'min_images': 8, 'optimal_images': 12},   # 中等稀有
            15: {'impact': 0.0038, 'min_images': 5, 'optimal_images': 10},   # 中等稀有
            11: {'impact': 0.0035, 'min_images': 8, 'optimal_images': 12},   # 中等稀有
        }
    
    def calculate_cumulative_impact(self, selected_classes: List[int]) -> float:
        """计算选定类别的累积影响"""
        total_impact = 0
        for class_id in selected_classes:
            if class_id in self.class_priorities:
                # 考虑边际效应递减
                base_impact = self.class_priorities[class_id]['impact']
                # 后续类别的影响会递减
                position_factor = 1.0 - (len([c for c in selected_classes if selected_classes.index(c) < selected_classes.index(class_id)]) * 0.1)
                position_factor = max(0.3, position_factor)  # 最低保持30%效果
                total_impact += base_impact * position_factor
        
        return total_impact
    
    def find_optimal_class_combination(self, target_improvement: float) -> Tuple[List[int], int, float]:
        """找到达到目标改进的最优类别组合"""
        # 按影响程度排序类别
        sorted_classes = sorted(self.class_priorities.keys(), 
                               key=lambda x: self.class_priorities[x]['impact'], 
                               reverse=True)
        
        best_combination = []
        best_images = float('inf')
        best_actual_improvement = 0
        
        # 尝试不同的类别组合
        for i in range(1, len(sorted_classes) + 1):
            current_classes = sorted_classes[:i]
            current_improvement = self.calculate_cumulative_impact(current_classes)
            
            if current_improvement >= target_improvement:
                # 计算所需图像数
                total_images = 0
                for class_id in current_classes:
                    # 根据目标改进程度决定使用最小还是最优图像数
                    if target_improvement <= 0.03:  # 小改进用最小图像数
                        total_images += self.class_priorities[class_id]['min_images']
                    else:  # 大改进用最优图像数
                        total_images += self.class_priorities[class_id]['optimal_images']
                
                if total_images < best_images:
                    best_combination = current_classes
                    best_images = total_images
                    best_actual_improvement = current_improvement
                break
        
        return best_combination, best_images, best_actual_improvement
    
    def calculate_all_thresholds(self) -> Dict[str, Dict]:
        """计算所有阈值的标注需求"""
        results = {}
        
        for threshold_name, target_miou in self.thresholds.items():
            target_improvement = target_miou - self.current_miou
            
            if target_improvement <= 0:
                results[threshold_name] = {
                    'target_miou': target_miou,
                    'target_improvement': target_improvement,
                    'status': 'already_achieved',
                    'required_images': 0,
                    'required_classes': [],
                    'actual_improvement': 0
                }
            else:
                classes, images, actual_improvement = self.find_optimal_class_combination(target_improvement)
                
                results[threshold_name] = {
                    'target_miou': target_miou,
                    'target_improvement': target_improvement,
                    'required_images': images,
                    'required_classes': classes,
                    'actual_improvement': actual_improvement,
                    'actual_miou': self.current_miou + actual_improvement,
                    'cost_estimate': images * 25,  # 假设每张25元
                    'time_estimate': images * 0.5,  # 假设每张30分钟
                    'efficiency': actual_improvement / images if images > 0 else 0
                }
        
        return results
    
    def calculate_incremental_strategy(self) -> List[Dict]:
        """计算渐进式标注策略"""
        strategy = []
        cumulative_images = 0
        cumulative_improvement = 0
        remaining_classes = list(self.class_priorities.keys())
        
        # 按优先级逐步添加类别
        for class_id in sorted(remaining_classes, key=lambda x: self.class_priorities[x]['impact'], reverse=True):
            class_info = self.class_priorities[class_id]
            
            # 计算这个类别的贡献
            position_factor = 1.0 - (len(strategy) * 0.1)
            position_factor = max(0.3, position_factor)
            
            class_improvement = class_info['impact'] * position_factor
            class_images = class_info['optimal_images']
            
            cumulative_images += class_images
            cumulative_improvement += class_improvement
            new_miou = self.current_miou + cumulative_improvement
            
            strategy.append({
                'step': len(strategy) + 1,
                'class_id': class_id,
                'class_images': class_images,
                'cumulative_images': cumulative_images,
                'class_improvement': class_improvement,
                'cumulative_improvement': cumulative_improvement,
                'new_miou': new_miou,
                'cost': cumulative_images * 25,
                'time_hours': cumulative_images * 0.5,
                'efficiency': cumulative_improvement / cumulative_images,
                'milestone': self._get_milestone(new_miou)
            })
        
        return strategy
    
    def _get_milestone(self, miou: float) -> str:
        """获取mIoU对应的里程碑"""
        if miou >= 0.60:
            return "SOTA性能"
        elif miou >= 0.55:
            return "优秀性能"
        elif miou >= 0.52:
            return "良好性能"
        elif miou >= 0.50:
            return "论文级别"
        elif miou >= 0.49:
            return "显著改进"
        elif miou >= 0.47:
            return "明显改进"
        elif miou >= 0.46:
            return "最小改进"
        else:
            return "基线水平"
    
    def find_minimum_viable_annotation(self) -> Dict:
        """找到最小可行标注方案"""
        # 只标注影响最大的类别28
        class_28_impact = self.class_priorities[28]['impact']
        min_images = self.class_priorities[28]['min_images']
        
        return {
            'strategy': 'minimum_viable',
            'target_class': 28,
            'required_images': min_images,
            'expected_improvement': class_28_impact,
            'expected_miou': self.current_miou + class_28_impact,
            'cost': min_images * 25,
            'time_hours': min_images * 0.5,
            'roi': class_28_impact / (min_images * 25) * 1000,  # ROI per 1000元
            'description': '仅补充完全缺失的类别28，获得最大单类别收益'
        }
    
    def generate_recommendation_matrix(self) -> Dict[str, Dict]:
        """生成推荐矩阵"""
        thresholds = self.calculate_all_thresholds()
        incremental = self.calculate_incremental_strategy()
        minimum = self.find_minimum_viable_annotation()
        
        # 创建推荐矩阵
        recommendations = {
            'budget_constrained': {  # 预算受限
                'low_budget': {
                    'budget_range': '0-500元',
                    'recommended_images': minimum['required_images'],
                    'expected_miou': minimum['expected_miou'],
                    'strategy': '仅补充类别28'
                },
                'medium_budget': {
                    'budget_range': '500-1500元',
                    'recommended_images': thresholds['paper_level']['required_images'],
                    'expected_miou': thresholds['paper_level']['actual_miou'],
                    'strategy': '达到论文级别'
                },
                'high_budget': {
                    'budget_range': '1500-3000元',
                    'recommended_images': thresholds['excellent_performance']['required_images'],
                    'expected_miou': thresholds['excellent_performance']['actual_miou'],
                    'strategy': '追求优秀性能'
                }
            },
            'performance_target': {  # 性能目标导向
                'paper_submission': thresholds['paper_level'],
                'commercial_application': thresholds['good_performance'],
                'research_benchmark': thresholds['excellent_performance'],
                'sota_competition': thresholds['sota_performance']
            },
            'time_constrained': {  # 时间受限
                'urgent': {
                    'time_limit': '1周',
                    'max_images': 20,
                    'strategy': incremental[0],
                    'description': '紧急情况下的最小改进'
                },
                'normal': {
                    'time_limit': '1个月',
                    'max_images': 80,
                    'strategy': incremental[3] if len(incremental) > 3 else incremental[-1],
                    'description': '正常时间安排下的平衡方案'
                },
                'relaxed': {
                    'time_limit': '3个月',
                    'max_images': 150,
                    'strategy': incremental[-1],
                    'description': '充足时间下的完整优化'
                }
            }
        }
        
        return recommendations
    
    def run_complete_analysis(self) -> Dict:
        """运行完整分析"""
        return {
            'current_baseline': {
                'miou': self.current_miou,
                'images': self.current_images
            },
            'thresholds': self.calculate_all_thresholds(),
            'incremental_strategy': self.calculate_incremental_strategy(),
            'minimum_viable': self.find_minimum_viable_annotation(),
            'recommendations': self.generate_recommendation_matrix(),
            'class_priorities': self.class_priorities
        }

def main():
    """主函数"""
    print("📊 标注阈值计算器")
    print("=" * 60)
    
    calculator = AnnotationThresholdCalculator()
    results = calculator.run_complete_analysis()
    
    # 显示关键阈值
    print("🎯 关键性能阈值分析:")
    print("-" * 60)
    
    thresholds = results['thresholds']
    for name, data in thresholds.items():
        if data.get('required_images', 0) > 0:
            print(f"\n{name.replace('_', ' ').title()}:")
            print(f"   目标mIoU: {data['target_miou']:.4f}")
            print(f"   需要图像: {data['required_images']} 张")
            print(f"   目标类别: {data['required_classes']}")
            print(f"   预计成本: {data['cost_estimate']} 元")
            print(f"   预计时间: {data['time_estimate']:.1f} 小时")
            print(f"   效率指标: {data['efficiency']:.6f} mIoU/张")
    
    # 显示最小可行方案
    print(f"\n🚀 最小可行标注方案:")
    print("-" * 60)
    minimum = results['minimum_viable']
    print(f"策略: {minimum['description']}")
    print(f"目标类别: {minimum['target_class']}")
    print(f"最少图像: {minimum['required_images']} 张")
    print(f"预期mIoU: {minimum['expected_miou']:.4f}")
    print(f"预计成本: {minimum['cost']} 元")
    print(f"ROI评分: {minimum['roi']:.2f}")
    
    # 显示渐进式策略
    print(f"\n📈 渐进式标注策略 (前5步):")
    print("-" * 60)
    incremental = results['incremental_strategy']
    for step in incremental[:5]:
        print(f"第{step['step']}步: 类别{step['class_id']} "
              f"({step['class_images']}张) → "
              f"mIoU {step['new_miou']:.4f} "
              f"({step['milestone']})")
    
    # 显示推荐矩阵
    print(f"\n💡 推荐方案矩阵:")
    print("-" * 60)
    
    recommendations = results['recommendations']
    
    print("预算导向:")
    for budget_type, budget_data in recommendations['budget_constrained'].items():
        print(f"  {budget_type}: {budget_data['budget_range']} → "
              f"{budget_data['recommended_images']}张 → "
              f"mIoU {budget_data['expected_miou']:.4f}")
    
    print("\n性能导向:")
    for perf_type, perf_data in recommendations['performance_target'].items():
        if perf_data.get('required_images', 0) > 0:
            print(f"  {perf_type}: {perf_data['required_images']}张 → "
                  f"mIoU {perf_data['actual_miou']:.4f}")
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'annotation_threshold_analysis_{timestamp}.json'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📁 详细分析结果已保存: {output_file}")

if __name__ == "__main__":
    main()
