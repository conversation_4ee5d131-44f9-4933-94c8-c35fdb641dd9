# UNet++ 训练配置文件
# Phase 2: 架构升级配置

# 基础配置
basic:
  project_name: "unet_plus_plus_optimization"
  experiment_name: "phase2_architecture_upgrade"
  num_classes: 29
  device: "cuda"
  seed: 42

# 模型配置
model:
  architecture: "unet_plus_plus"
  backbone: "resnet50"  # resnet50, resnet101, efficientnet-b4
  pretrained: true
  deep_supervision: true
  use_attention: true
  use_cbam: true
  
  # UNet++特定配置
  decoder_channels: [256, 128, 64, 32, 16]
  encoder_depth: 5
  
  # 注意力机制配置
  attention_type: "cbam"  # cbam, se, eca
  attention_reduction: 16

# 数据配置
data:
  # 数据路径
  train_images: "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/JPEGImages"
  train_labels: "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/SegmentationClass"
  val_images: "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/JPEGImages"
  val_labels: "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/SegmentationClass"
  
  # 数据预处理
  input_size: [512, 512]
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  
  # 数据增强
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.2
    rotation: 15
    scale: [0.8, 1.2]
    brightness: 0.2
    contrast: 0.2
    saturation: 0.2
    hue: 0.1

# 训练配置
training:
  # 基础训练参数
  epochs: 150
  batch_size: 8  # 根据GPU内存调整
  accumulate_grad_batches: 2  # 梯度累积
  
  # 优化器配置
  optimizer:
    type: "AdamW"
    lr: 2e-4
    weight_decay: 1e-4
    betas: [0.9, 0.999]
    eps: 1e-8
  
  # 学习率调度
  scheduler:
    type: "CosineAnnealingWarmRestarts"
    T_0: 30
    T_mult: 2
    eta_min: 1e-6
    warmup_epochs: 5
    warmup_lr: 1e-5
  
  # 混合精度训练
  mixed_precision: true
  
  # 梯度裁剪
  gradient_clipping: 1.0

# 损失函数配置
loss:
  # 深度监督权重
  deep_supervision_weights: [0.1, 0.2, 0.3, 0.4]
  
  # 主损失函数
  primary_loss:
    type: "combined"
    components:
      cross_entropy:
        weight: 0.4
        class_weights: null  # 可以设置类别权重
      focal_loss:
        weight: 0.3
        alpha: 0.25
        gamma: 2.0
      dice_loss:
        weight: 0.3
        smooth: 1.0
  
  # 辅助损失函数
  auxiliary_losses:
    boundary_loss:
      weight: 0.1
      enabled: false
    consistency_loss:
      weight: 0.05
      enabled: false

# 验证配置
validation:
  frequency: 1  # 每个epoch验证一次
  metrics: ["miou", "pixel_accuracy", "class_accuracy"]
  save_predictions: false
  
  # 后处理配置
  postprocessing:
    enabled: true
    config: "balanced"  # fast, balanced, high_quality

# 保存配置
checkpointing:
  save_dir: "checkpoints/unet_plus_plus"
  save_frequency: 5
  save_best: true
  save_last: true
  monitor_metric: "val_miou"
  mode: "max"

# 日志配置
logging:
  log_dir: "logs/unet_plus_plus"
  tensorboard: true
  wandb:
    enabled: false
    project: "semantic_segmentation"
    entity: null
  
  # 日志频率
  log_frequency: 10
  image_log_frequency: 50

# 早停配置
early_stopping:
  enabled: true
  patience: 25
  min_delta: 0.001
  monitor: "val_miou"
  mode: "max"

# 模型集成配置
ensemble:
  enabled: false
  models: []
  weights: []

# 测试配置
testing:
  # 测试时增强
  tta:
    enabled: true
    scales: [0.75, 1.0, 1.25]
    flips: ["none", "horizontal"]
  
  # 后处理
  postprocessing:
    enabled: true
    config: "high_quality"

# 硬件配置
hardware:
  num_workers: 4
  pin_memory: true
  persistent_workers: true
  
  # GPU配置
  gpu_ids: [0]  # 使用的GPU ID
  distributed: false

# 调试配置
debug:
  enabled: false
  fast_dev_run: false
  overfit_batches: 0
  limit_train_batches: 1.0
  limit_val_batches: 1.0

# 恢复训练配置
resume:
  enabled: false
  checkpoint_path: null
  resume_optimizer: true
  resume_scheduler: true

# 预训练模型配置
pretrained:
  # 从之前的模型继续训练
  load_from_previous: true
  previous_model_path: "best_smart_optimized_miou_0.4477.pth"
  strict_loading: false
  
  # 冻结配置
  freeze_encoder: false
  freeze_epochs: 0

# 高级配置
advanced:
  # 知识蒸馏
  knowledge_distillation:
    enabled: false
    teacher_model: null
    temperature: 4.0
    alpha: 0.7
  
  # 自监督学习
  self_supervised:
    enabled: false
    method: "rotation"
    weight: 0.1
  
  # 对抗训练
  adversarial_training:
    enabled: false
    epsilon: 0.01
    alpha: 0.005
    steps: 7

# 评估配置
evaluation:
  # 评估指标
  metrics:
    - "miou"
    - "pixel_accuracy" 
    - "class_accuracy"
    - "dice_score"
    - "hausdorff_distance"
  
  # 类别特定评估
  per_class_metrics: true
  
  # 可视化
  visualization:
    enabled: true
    num_samples: 10
    save_dir: "visualizations"

# 超参数搜索配置
hyperparameter_search:
  enabled: false
  method: "random"  # random, grid, bayesian
  trials: 20
  
  search_space:
    lr: [1e-5, 1e-3]
    weight_decay: [1e-5, 1e-3]
    batch_size: [4, 8, 16]
