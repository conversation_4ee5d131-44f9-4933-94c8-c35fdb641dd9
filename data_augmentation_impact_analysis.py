#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据增强影响分析工具
评估增加手动标注数据对模型性能的潜在帮助
"""

import numpy as np
import matplotlib.pyplot as plt
import json
from typing import Dict, List, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAugmentationImpactAnalyzer:
    """数据增强影响分析器"""
    
    def __init__(self):
        # 当前数据集统计 (基于之前的分析)
        self.current_stats = {
            'total_images': 120,
            'total_pixels': 1911029760,
            'class_pixel_counts': [
                436005864, 74746544, 25098761, 390196128, 89288916, 20510753,
                159687065, 12945223, 2008146, 3880577, 20635851, 8069839,
                77034327, 193246789, 52124719, 7778484, 106484589, 1962919,
                3313291, 3159535, 42600710, 56176240, 1948310, 14311202,
                66197477, 36793389, 4276662, 547450, 0
            ],
            'class_image_counts': [
                120, 81, 17, 116, 83, 59, 106, 24, 9, 59, 13, 71,
                94, 93, 32, 12, 49, 19, 8, 18, 55, 48, 18, 17,
                84, 18, 17, 9, 0
            ]
        }
        
        # 类别分类
        self.missing_classes = [28]  # 完全缺失
        self.extremely_rare_classes = [27]  # <0.05%
        self.very_rare_classes = [8, 17, 22]  # 0.05%-0.15%
        self.rare_classes = [9, 11, 15, 18, 19, 26]  # 0.15%-0.5%
        self.dominant_classes = [0, 3, 13]  # >10%
        
    def calculate_current_performance_baseline(self) -> Dict[str, float]:
        """计算当前性能基线"""
        total_pixels = self.current_stats['total_pixels']
        class_pixel_counts = self.current_stats['class_pixel_counts']
        
        # 计算各类别的像素比例
        pixel_ratios = [count / total_pixels for count in class_pixel_counts]
        
        # 基于像素比例预测各类别的IoU (经验公式)
        predicted_ious = []
        for i, ratio in enumerate(pixel_ratios):
            if i in self.missing_classes:
                iou = 0.0  # 完全缺失
            elif i in self.extremely_rare_classes:
                iou = min(0.05, ratio * 100)  # 极稀有类别很难学习
            elif i in self.very_rare_classes:
                iou = min(0.15, ratio * 50)  # 很稀有类别
            elif i in self.rare_classes:
                iou = min(0.35, ratio * 20)  # 稀有类别
            elif i in self.dominant_classes:
                iou = min(0.85, 0.6 + ratio * 1.5)  # 主导类别容易学习
            else:
                iou = min(0.75, 0.4 + ratio * 10)  # 正常类别
            
            predicted_ious.append(iou)
        
        # 计算平均mIoU
        valid_classes = [i for i in range(29) if i not in self.missing_classes]
        avg_miou = np.mean([predicted_ious[i] for i in valid_classes])
        
        return {
            'predicted_ious': predicted_ious,
            'avg_miou': avg_miou,
            'valid_classes': len(valid_classes)
        }
    
    def simulate_data_augmentation_scenarios(self) -> Dict[str, Dict]:
        """模拟不同数据增强场景"""
        scenarios = {}
        
        # 场景1: 最小增强 - 只补充缺失类别
        scenarios['minimal'] = self._simulate_scenario(
            additional_images=20,
            target_classes=[28],
            target_pixels_per_class={28: 10000000}  # 约0.5%像素
        )
        
        # 场景2: 保守增强 - 补充缺失和极稀有类别
        scenarios['conservative'] = self._simulate_scenario(
            additional_images=50,
            target_classes=[27, 28],
            target_pixels_per_class={
                27: 20000000,  # 提升到约1%
                28: 15000000   # 新增约0.8%
            }
        )
        
        # 场景3: 积极增强 - 补充所有稀有类别
        scenarios['aggressive'] = self._simulate_scenario(
            additional_images=100,
            target_classes=[8, 17, 18, 19, 22, 27, 28],
            target_pixels_per_class={
                8: 15000000,   # 提升到约0.8%
                17: 12000000,  # 提升到约0.6%
                18: 12000000,  # 提升到约0.6%
                19: 12000000,  # 提升到约0.6%
                22: 12000000,  # 提升到约0.6%
                27: 25000000,  # 提升到约1.3%
                28: 20000000   # 新增约1.0%
            }
        )
        
        # 场景4: 完全平衡 - 所有类别达到相对平衡
        scenarios['balanced'] = self._simulate_scenario(
            additional_images=200,
            target_classes=list(range(29)),
            target_pixels_per_class={i: max(30000000, self.current_stats['class_pixel_counts'][i]) 
                                   for i in range(29)}  # 每个类别至少1.5%
        )
        
        return scenarios
    
    def _simulate_scenario(self, additional_images: int, target_classes: List[int], 
                          target_pixels_per_class: Dict[int, int]) -> Dict:
        """模拟特定增强场景"""
        # 计算新的像素分布
        new_pixel_counts = self.current_stats['class_pixel_counts'].copy()
        new_image_counts = self.current_stats['class_image_counts'].copy()
        
        total_additional_pixels = 0
        for class_id, target_pixels in target_pixels_per_class.items():
            if class_id in target_classes:
                additional_pixels = max(0, target_pixels - new_pixel_counts[class_id])
                new_pixel_counts[class_id] += additional_pixels
                total_additional_pixels += additional_pixels
                
                # 估算需要的额外图像数
                if class_id in self.missing_classes:
                    new_image_counts[class_id] = max(10, additional_images // len(target_classes))
                else:
                    additional_images_for_class = max(5, additional_pixels // 1000000)
                    new_image_counts[class_id] += additional_images_for_class
        
        # 计算新的总像素数
        new_total_pixels = self.current_stats['total_pixels'] + total_additional_pixels
        new_total_images = self.current_stats['total_images'] + additional_images
        
        # 计算新的像素比例
        new_pixel_ratios = [count / new_total_pixels for count in new_pixel_counts]
        
        # 预测新的IoU
        predicted_ious = []
        for i, ratio in enumerate(new_pixel_ratios):
            if ratio == 0:
                iou = 0.0
            elif ratio < 0.001:  # <0.1%
                iou = min(0.20, ratio * 200)
            elif ratio < 0.005:  # 0.1%-0.5%
                iou = min(0.45, 0.15 + ratio * 60)
            elif ratio < 0.02:   # 0.5%-2%
                iou = min(0.65, 0.35 + ratio * 15)
            elif ratio < 0.1:    # 2%-10%
                iou = min(0.80, 0.55 + ratio * 2.5)
            else:                # >10%
                iou = min(0.85, 0.70 + ratio * 0.75)
            
            predicted_ious.append(iou)
        
        # 计算平均mIoU
        valid_classes = [i for i in range(29) if new_pixel_counts[i] > 0]
        avg_miou = np.mean([predicted_ious[i] for i in valid_classes])
        
        return {
            'additional_images': additional_images,
            'new_total_images': new_total_images,
            'new_pixel_counts': new_pixel_counts,
            'new_pixel_ratios': new_pixel_ratios,
            'predicted_ious': predicted_ious,
            'avg_miou': avg_miou,
            'valid_classes': len(valid_classes),
            'target_classes': target_classes,
            'improvement_per_class': {
                i: predicted_ious[i] - self.calculate_current_performance_baseline()['predicted_ious'][i]
                for i in target_classes
            }
        }
    
    def calculate_cost_benefit_analysis(self, scenarios: Dict[str, Dict]) -> Dict[str, Dict]:
        """计算成本效益分析"""
        baseline = self.calculate_current_performance_baseline()
        baseline_miou = baseline['avg_miou']
        
        analysis = {}
        
        for scenario_name, scenario_data in scenarios.items():
            miou_improvement = scenario_data['avg_miou'] - baseline_miou
            additional_images = scenario_data['additional_images']
            
            # 估算标注成本 (假设每张图像需要30分钟高质量标注)
            annotation_hours = additional_images * 0.5  # 30分钟 = 0.5小时
            annotation_cost = annotation_hours * 50  # 假设每小时50元
            
            # 计算效益指标
            improvement_per_image = miou_improvement / additional_images if additional_images > 0 else 0
            improvement_per_hour = miou_improvement / annotation_hours if annotation_hours > 0 else 0
            improvement_per_yuan = miou_improvement / annotation_cost if annotation_cost > 0 else 0
            
            analysis[scenario_name] = {
                'miou_improvement': miou_improvement,
                'improvement_percentage': (miou_improvement / baseline_miou) * 100,
                'additional_images': additional_images,
                'annotation_hours': annotation_hours,
                'annotation_cost': annotation_cost,
                'improvement_per_image': improvement_per_image,
                'improvement_per_hour': improvement_per_hour,
                'improvement_per_yuan': improvement_per_yuan,
                'roi_score': (miou_improvement * 1000) / annotation_hours  # ROI评分
            }
        
        return analysis
    
    def generate_recommendations(self, scenarios: Dict[str, Dict], 
                               cost_benefit: Dict[str, Dict]) -> Dict[str, str]:
        """生成推荐建议"""
        recommendations = {}
        
        # 找出最佳ROI场景
        best_roi_scenario = max(cost_benefit.keys(), 
                               key=lambda x: cost_benefit[x]['roi_score'])
        
        # 找出最大改进场景
        best_improvement_scenario = max(cost_benefit.keys(),
                                      key=lambda x: cost_benefit[x]['miou_improvement'])
        
        recommendations['best_roi'] = best_roi_scenario
        recommendations['best_improvement'] = best_improvement_scenario
        
        # 生成具体建议
        if cost_benefit[best_roi_scenario]['roi_score'] > 10:
            recommendations['priority'] = 'high'
            recommendations['action'] = f"强烈推荐实施{best_roi_scenario}方案"
        elif cost_benefit[best_roi_scenario]['roi_score'] > 5:
            recommendations['priority'] = 'medium'
            recommendations['action'] = f"建议考虑{best_roi_scenario}方案"
        else:
            recommendations['priority'] = 'low'
            recommendations['action'] = "数据增强的性价比较低，建议优先优化训练策略"
        
        return recommendations
    
    def run_complete_analysis(self) -> Dict:
        """运行完整分析"""
        logger.info("🔍 开始数据增强影响分析...")
        
        # 计算基线性能
        baseline = self.calculate_current_performance_baseline()
        logger.info(f"📊 当前基线mIoU: {baseline['avg_miou']:.4f}")
        
        # 模拟不同场景
        scenarios = self.simulate_data_augmentation_scenarios()
        logger.info(f"🎯 模拟了 {len(scenarios)} 个增强场景")
        
        # 成本效益分析
        cost_benefit = self.calculate_cost_benefit_analysis(scenarios)
        logger.info("💰 完成成本效益分析")
        
        # 生成推荐
        recommendations = self.generate_recommendations(scenarios, cost_benefit)
        logger.info(f"💡 推荐方案: {recommendations['action']}")
        
        return {
            'baseline': baseline,
            'scenarios': scenarios,
            'cost_benefit': cost_benefit,
            'recommendations': recommendations
        }

def main():
    """主函数"""
    print("📊 数据增强影响分析")
    print("=" * 60)
    
    analyzer = DataAugmentationImpactAnalyzer()
    results = analyzer.run_complete_analysis()
    
    # 显示结果
    baseline = results['baseline']
    scenarios = results['scenarios']
    cost_benefit = results['cost_benefit']
    recommendations = results['recommendations']
    
    print(f"\n📈 当前基线性能:")
    print(f"   平均mIoU: {baseline['avg_miou']:.4f}")
    print(f"   有效类别: {baseline['valid_classes']}/29")
    
    print(f"\n🎯 数据增强场景分析:")
    print("-" * 60)
    
    for scenario_name, scenario_data in scenarios.items():
        cb_data = cost_benefit[scenario_name]
        
        print(f"\n{scenario_name.upper()} 方案:")
        print(f"   额外图像: {scenario_data['additional_images']} 张")
        print(f"   预期mIoU: {scenario_data['avg_miou']:.4f}")
        print(f"   性能提升: +{cb_data['miou_improvement']:.4f} ({cb_data['improvement_percentage']:.1f}%)")
        print(f"   标注成本: {cb_data['annotation_cost']:.0f} 元 ({cb_data['annotation_hours']:.1f} 小时)")
        print(f"   ROI评分: {cb_data['roi_score']:.2f}")
        print(f"   目标类别: {scenario_data['target_classes']}")
    
    print(f"\n💡 推荐建议:")
    print(f"   优先级: {recommendations['priority'].upper()}")
    print(f"   行动建议: {recommendations['action']}")
    print(f"   最佳ROI: {recommendations['best_roi'].upper()}")
    print(f"   最大改进: {recommendations['best_improvement'].upper()}")
    
    # 保存详细结果
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'data_augmentation_analysis_{timestamp}.json'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📁 详细分析结果已保存: {output_file}")

if __name__ == "__main__":
    main()
