#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from scipy import ndimage
from scipy.ndimage import label
import logging
from typing import Tuple, List, Optional, Dict
import time

# 可选依赖
try:
    from skimage import morphology, measure, segmentation
    from skimage.segmentation import watershed
    from skimage.feature import peak_local_maxima
    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False

try:
    import pydensecrf.densecrf as dcrf
    from pydensecrf.utils import unary_from_softmax
    CRF_AVAILABLE = True
except ImportError:
    CRF_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedPostProcessor:
    """增强版后处理器 - 论文级别的后处理技术"""

    def __init__(self, num_classes=29, device='cuda'):
        self.num_classes = num_classes
        self.device = device

        # 后处理配置
        self.config = {
            'use_tta': True,                    # 测试时增强
            'use_crf': True,                    # 条件随机场
            'use_morphology': True,             # 形态学操作
            'use_watershed': True,              # 分水岭算法
            'use_connected_components': True,   # 连通组件分析
            'use_confidence_filtering': True,   # 置信度过滤
            'use_boundary_refinement': True,    # 边界细化
            'use_multi_scale_fusion': True,     # 多尺度融合
            'use_superpixel_voting': True,      # 超像素投票
            'use_edge_aware_smoothing': True,   # 边缘感知平滑
            'use_class_specific_processing': True,  # 类别特定处理
        }

        # TTA配置
        self.tta_config = {
            'scales': [0.75, 1.0, 1.25, 1.5],
            'flips': ['none', 'horizontal', 'vertical', 'both'],
            'rotations': [0, 90, 180, 270],
            'use_all_combinations': False,  # 如果True会很慢但效果更好
        }

        # CRF配置
        self.crf_config = {
            'iterations': 10,
            'pos_w': 3,
            'pos_xy_std': 3,
            'bi_w': 4,
            'bi_xy_std': 49,
            'bi_rgb_std': 5,
        }

        # 形态学操作参数
        self.morph_params = {
            'opening_kernel': 3,
            'closing_kernel': 5,
            'erosion_kernel': 2,
            'dilation_kernel': 3,
        }

        # 连通组件参数
        self.cc_params = {
            'min_size': 50,
            'max_size': 50000,
        }

        # 置信度过滤参数
        self.conf_params = {
            'min_confidence': 0.3,
            'adaptive_threshold': True,
            'percentile': 0.3,
        }

    def process_prediction(self, logits, original_image=None, original_size=None):
        """
        对模型预测结果进行全面后处理

        Args:
            logits: 模型输出 [B, C, H, W] 或 [C, H, W]
            original_image: 原始图像 [H, W, 3] (用于CRF)
            original_size: 原始图像尺寸 (H, W)

        Returns:
            processed_pred: 后处理后的预测结果
            confidence_map: 置信度图
            processing_info: 处理信息
        """
        start_time = time.time()
        processing_info = {}

        if len(logits.shape) == 4:
            logits = logits[0]  # 移除batch维度

        # 1. 基础softmax处理
        probs = F.softmax(logits, dim=0)  # [C, H, W]

        # 2. 多尺度融合
        if self.config['use_multi_scale_fusion']:
            probs = self._multi_scale_fusion(probs)
            processing_info['multi_scale_fusion'] = True

        # 3. 置信度过滤
        if self.config['use_confidence_filtering']:
            probs, confidence_map = self._confidence_filtering(probs)
            processing_info['confidence_filtering'] = True
        else:
            confidence_map = torch.max(probs, dim=0)[0]

        # 4. 获取初始预测
        pred = torch.argmax(probs, dim=0).cpu().numpy()

        # 5. CRF后处理
        if self.config['use_crf'] and CRF_AVAILABLE and original_image is not None:
            pred = self._crf_postprocessing(probs.cpu().numpy(), original_image, pred)
            processing_info['crf'] = True

        # 6. 形态学操作
        if self.config['use_morphology']:
            pred = self._morphological_operations(pred)
            processing_info['morphology'] = True

        # 7. 连通组件分析
        if self.config['use_connected_components']:
            pred = self._connected_components_analysis(pred)
            processing_info['connected_components'] = True

        # 8. 分水岭算法
        if self.config['use_watershed']:
            pred = self._watershed_segmentation(pred, probs.cpu().numpy())
            processing_info['watershed'] = True

        # 9. 边界细化
        if self.config['use_boundary_refinement']:
            pred = self._boundary_refinement(pred, probs.cpu().numpy())
            processing_info['boundary_refinement'] = True

        # 10. 超像素投票
        if self.config['use_superpixel_voting'] and original_image is not None:
            pred = self._superpixel_voting(pred, original_image)
            processing_info['superpixel_voting'] = True

        # 11. 边缘感知平滑
        if self.config['use_edge_aware_smoothing'] and original_image is not None:
            pred = self._edge_aware_smoothing(pred, original_image)
            processing_info['edge_aware_smoothing'] = True

        # 12. 类别特定处理
        if self.config['use_class_specific_processing']:
            pred = self._class_specific_processing(pred, probs.cpu().numpy())
            processing_info['class_specific_processing'] = True

        # 13. 调整到原始尺寸
        if original_size is not None:
            pred = cv2.resize(pred.astype(np.uint8),
                            (original_size[1], original_size[0]),
                            interpolation=cv2.INTER_NEAREST)

        processing_time = time.time() - start_time
        processing_info['total_time'] = processing_time

        logger.info(f"后处理完成，耗时: {processing_time:.3f}s")

        return pred, confidence_map.cpu().numpy(), processing_info

    def test_time_augmentation(self, model, image_tensor):
        """增强版测试时增强"""
        predictions = []

        # 基础增强
        basic_augs = [
            lambda x: x,  # 原图
            lambda x: torch.flip(x, dims=[3]),  # 水平翻转
            lambda x: torch.flip(x, dims=[2]),  # 垂直翻转
            lambda x: torch.flip(torch.flip(x, dims=[3]), dims=[2]),  # 水平+垂直翻转
        ]

        basic_inv_augs = [
            lambda x: x,
            lambda x: torch.flip(x, dims=[3]),
            lambda x: torch.flip(x, dims=[2]),
            lambda x: torch.flip(torch.flip(x, dims=[3]), dims=[2]),
        ]

        with torch.no_grad():
            # 多尺度TTA
            for scale in self.tta_config['scales']:
                if scale != 1.0:
                    # 缩放图像
                    h, w = image_tensor.shape[2], image_tensor.shape[3]
                    new_h, new_w = int(h * scale), int(w * scale)
                    scaled_image = F.interpolate(
                        image_tensor, size=(new_h, new_w),
                        mode='bilinear', align_corners=False
                    )
                else:
                    scaled_image = image_tensor

                # 对缩放后的图像应用基础增强
                for aug, inv_aug in zip(basic_augs, basic_inv_augs):
                    aug_image = aug(scaled_image)
                    output = model(aug_image)
                    output = inv_aug(output)

                    # 恢复原尺寸
                    if scale != 1.0:
                        output = F.interpolate(
                            output, size=(h, w),
                            mode='bilinear', align_corners=False
                        )

                    predictions.append(output)

        # 加权平均所有预测
        weights = [1.0] * len(predictions)  # 可以根据需要调整权重
        ensemble_output = sum(pred * w for pred, w in zip(predictions, weights)) / sum(weights)

        return ensemble_output

    def _multi_scale_fusion(self, probs):
        """增强版多尺度融合"""
        scales = [0.5, 0.75, 1.0, 1.25, 1.5]
        weights = [0.1, 0.2, 0.4, 0.2, 0.1]  # 中心尺度权重更高

        fused_probs = torch.zeros_like(probs)

        for scale, weight in zip(scales, weights):
            if scale != 1.0:
                h, w = probs.shape[1], probs.shape[2]
                new_h, new_w = int(h * scale), int(w * scale)

                # 缩放
                scaled_probs = F.interpolate(
                    probs.unsqueeze(0),
                    size=(new_h, new_w),
                    mode='bilinear',
                    align_corners=False
                )[0]

                # 恢复原尺寸
                scaled_probs = F.interpolate(
                    scaled_probs.unsqueeze(0),
                    size=(h, w),
                    mode='bilinear',
                    align_corners=False
                )[0]
            else:
                scaled_probs = probs

            fused_probs += scaled_probs * weight

        return fused_probs

    def _confidence_filtering(self, probs):
        """增强版置信度过滤"""
        confidence_map = torch.max(probs, dim=0)[0]

        if self.conf_params['adaptive_threshold']:
            # 使用多个百分位数的组合
            thresholds = [
                torch.quantile(confidence_map, 0.2),
                torch.quantile(confidence_map, 0.3),
                torch.quantile(confidence_map, 0.4),
            ]
            threshold = torch.mean(torch.stack(thresholds))
            threshold = max(threshold.item(), self.conf_params['min_confidence'])
        else:
            threshold = self.conf_params['min_confidence']

        # 渐进式置信度过滤
        low_conf_mask = confidence_map < threshold
        medium_conf_mask = (confidence_map >= threshold) & (confidence_map < threshold * 1.5)

        probs_filtered = probs.clone()

        # 低置信度区域设为背景
        probs_filtered[:, low_conf_mask] = 0
        probs_filtered[0, low_conf_mask] = 1

        # 中等置信度区域降权
        probs_filtered[:, medium_conf_mask] *= 0.8

        logger.info(f"置信度过滤: 阈值={threshold:.3f}, 低置信度像素={low_conf_mask.sum().item()}")

        return probs_filtered, confidence_map

    def _crf_postprocessing(self, probs, original_image, pred):
        """条件随机场后处理"""
        if not CRF_AVAILABLE:
            logger.warning("pydensecrf不可用，跳过CRF后处理")
            return pred

        try:
            h, w = pred.shape

            # 调整图像尺寸匹配预测
            if original_image.shape[:2] != (h, w):
                image = cv2.resize(original_image, (w, h))
            else:
                image = original_image.copy()

            # 创建CRF模型
            d = dcrf.DenseCRF2D(w, h, self.num_classes)

            # 设置一元势能
            unary = unary_from_softmax(probs)
            d.setUnaryEnergy(unary)

            # 添加成对势能
            d.addPairwiseGaussian(sxy=(self.crf_config['pos_xy_std'], self.crf_config['pos_xy_std']),
                                compat=self.crf_config['pos_w'])

            d.addPairwiseBilateral(sxy=(self.crf_config['bi_xy_std'], self.crf_config['bi_xy_std']),
                                 srgb=(self.crf_config['bi_rgb_std'], self.crf_config['bi_rgb_std']),
                                 rgbim=image.astype(np.uint8),
                                 compat=self.crf_config['bi_w'])

            # 推理
            Q = d.inference(self.crf_config['iterations'])
            MAP = np.argmax(Q, axis=0).reshape((h, w))

            logger.info("CRF后处理完成")
            return MAP

        except Exception as e:
            logger.warning(f"CRF后处理失败: {e}")
            return pred

    def _morphological_operations(self, pred):
        """增强版形态学操作"""
        processed_pred = pred.copy()

        # 类别特定的形态学参数
        class_morph_params = {
            # 小物体类别使用较小的核
            'small_objects': [1, 2, 3, 5, 6, 9, 16, 17],  # 例如：飞机、自行车、鸟、瓶子等
            'large_objects': [7, 8, 11, 19, 22, 23, 24, 25, 26, 27, 28],  # 例如：公交车、汽车、建筑等
        }

        for class_id in range(1, self.num_classes):
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() == 0:
                continue

            # 根据类别选择合适的核大小
            if class_id in class_morph_params['small_objects']:
                open_kernel_size = 2
                close_kernel_size = 3
            elif class_id in class_morph_params['large_objects']:
                open_kernel_size = 4
                close_kernel_size = 6
            else:
                open_kernel_size = self.morph_params['opening_kernel']
                close_kernel_size = self.morph_params['closing_kernel']

            # 开运算
            kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (open_kernel_size, open_kernel_size))
            class_mask = cv2.morphologyEx(class_mask, cv2.MORPH_OPEN, kernel_open)

            # 闭运算
            kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (close_kernel_size, close_kernel_size))
            class_mask = cv2.morphologyEx(class_mask, cv2.MORPH_CLOSE, kernel_close)

            # 更新预测结果
            processed_pred[class_mask == 1] = class_id
            processed_pred[(pred == class_id) & (class_mask == 0)] = 0

        return processed_pred

    def _connected_components_analysis(self, pred):
        """增强版连通组件分析"""
        processed_pred = pred.copy()

        # 类别特定的大小阈值
        class_size_thresholds = {
            # 小物体类别
            'small_objects': {'min_size': 20, 'max_size': 10000},
            # 大物体类别
            'large_objects': {'min_size': 100, 'max_size': 100000},
            # 背景类别
            'background_objects': {'min_size': 500, 'max_size': 200000},
        }

        small_objects = [1, 2, 3, 5, 6, 9, 16, 17]
        large_objects = [7, 8, 11, 19, 22, 23, 24, 25, 26, 27, 28]
        background_objects = [21, 22, 23, 24, 26, 27, 28]  # 墙、建筑、天空、地板、树、天花板、道路

        for class_id in range(1, self.num_classes):
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() == 0:
                continue

            # 选择合适的阈值
            if class_id in small_objects:
                thresholds = class_size_thresholds['small_objects']
            elif class_id in large_objects:
                thresholds = class_size_thresholds['large_objects']
            elif class_id in background_objects:
                thresholds = class_size_thresholds['background_objects']
            else:
                thresholds = self.cc_params

            # 连通组件标记
            num_labels, labels = cv2.connectedComponents(class_mask)

            for label_id in range(1, num_labels):
                component_mask = (labels == label_id)
                component_size = component_mask.sum()

                # 移除不合适大小的连通组件
                if (component_size < thresholds['min_size'] or
                    component_size > thresholds['max_size']):
                    processed_pred[component_mask] = 0

        return processed_pred

    def _watershed_segmentation(self, pred, probs):
        """增强版分水岭算法"""
        if not SKIMAGE_AVAILABLE:
            logger.warning("Skimage不可用，跳过分水岭算法")
            return pred

        processed_pred = pred.copy()

        for class_id in range(1, self.num_classes):
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() < 200:  # 跳过太小的区域
                continue

            try:
                # 使用概率图和距离变换的组合
                class_prob = probs[class_id]
                distance = ndimage.distance_transform_edt(class_mask)

                # 结合概率和距离信息
                combined_map = distance * (class_prob + 0.1)  # 避免零值

                # 寻找局部最大值
                if SKIMAGE_AVAILABLE:
                    local_maxima = peak_local_maxima(
                        combined_map,
                        min_distance=15,
                        threshold_abs=np.max(combined_map) * 0.3
                    )

                    if len(local_maxima[0]) > 1:
                        markers = np.zeros_like(distance, dtype=np.int32)
                        for i, (y, x) in enumerate(zip(local_maxima[0], local_maxima[1])):
                            markers[y, x] = i + 1

                        # 应用分水岭
                        labels = watershed(-combined_map, markers, mask=class_mask)

                        # 保持原类别标签
                        processed_pred[class_mask == 1] = class_id

            except Exception as e:
                logger.warning(f"分水岭算法处理类别{class_id}失败: {e}")
                continue

        return processed_pred

    def _boundary_refinement(self, pred, probs):
        """增强版边界细化"""
        processed_pred = pred.copy()

        # 多尺度边界检测
        kernels = [
            np.ones((3, 3), np.uint8),
            np.ones((5, 5), np.uint8),
        ]

        for kernel in kernels:
            for class_id in range(1, self.num_classes):
                class_mask = (pred == class_id).astype(np.uint8)

                if class_mask.sum() == 0:
                    continue

                # 边界检测
                eroded = cv2.erode(class_mask, kernel, iterations=1)
                boundary = class_mask - eroded

                if boundary.sum() == 0:
                    continue

                # 在边界区域重新分类
                boundary_coords = np.where(boundary == 1)

                for y, x in zip(boundary_coords[0], boundary_coords[1]):
                    # 获取更大邻域的概率分布
                    window_size = kernel.shape[0] // 2 + 1
                    y_start = max(0, y - window_size)
                    y_end = min(probs.shape[1], y + window_size + 1)
                    x_start = max(0, x - window_size)
                    x_end = min(probs.shape[2], x + window_size + 1)

                    neighborhood_probs = probs[:, y_start:y_end, x_start:x_end]

                    # 简单平均，避免权重维度问题
                    if neighborhood_probs.size > 0:
                        weighted_probs = np.mean(neighborhood_probs, axis=(1, 2))
                        new_class = np.argmax(weighted_probs)
                        processed_pred[y, x] = new_class

        return processed_pred

    def _superpixel_voting(self, pred, original_image):
        """超像素投票后处理"""
        try:
            from skimage.segmentation import slic
            from skimage.util import img_as_float

            # 调整图像尺寸
            h, w = pred.shape
            if original_image.shape[:2] != (h, w):
                image = cv2.resize(original_image, (w, h))
            else:
                image = original_image.copy()

            # 生成超像素
            image_float = img_as_float(image)
            segments = slic(image_float, n_segments=500, compactness=10, sigma=1)

            processed_pred = pred.copy()

            # 对每个超像素进行投票
            for segment_id in np.unique(segments):
                mask = segments == segment_id
                segment_labels = pred[mask]

                if len(segment_labels) == 0:
                    continue

                # 投票决定该超像素的标签
                unique_labels, counts = np.unique(segment_labels, return_counts=True)

                # 如果背景类占主导，需要更高的阈值
                if unique_labels[np.argmax(counts)] == 0:
                    if counts[np.argmax(counts)] / len(segment_labels) > 0.7:
                        majority_label = 0
                    else:
                        # 选择非背景类中最多的
                        non_bg_mask = unique_labels != 0
                        if np.any(non_bg_mask):
                            non_bg_labels = unique_labels[non_bg_mask]
                            non_bg_counts = counts[non_bg_mask]
                            majority_label = non_bg_labels[np.argmax(non_bg_counts)]
                        else:
                            majority_label = 0
                else:
                    majority_label = unique_labels[np.argmax(counts)]

                processed_pred[mask] = majority_label

            logger.info("超像素投票完成")
            return processed_pred

        except Exception as e:
            logger.warning(f"超像素投票失败: {e}")
            return pred

    def _edge_aware_smoothing(self, pred, original_image):
        """边缘感知平滑"""
        try:
            # 调整图像尺寸
            h, w = pred.shape
            if original_image.shape[:2] != (h, w):
                image = cv2.resize(original_image, (w, h))
            else:
                image = original_image.copy()

            # 检测边缘
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)

            # 膨胀边缘以创建边缘区域
            kernel = np.ones((3, 3), np.uint8)
            edge_regions = cv2.dilate(edges, kernel, iterations=2)

            processed_pred = pred.copy()

            # 在非边缘区域应用平滑
            non_edge_mask = edge_regions == 0

            # 使用中值滤波进行平滑
            smoothed = cv2.medianBlur(pred.astype(np.uint8), 5)

            # 只在非边缘区域应用平滑结果
            processed_pred[non_edge_mask] = smoothed[non_edge_mask]

            logger.info("边缘感知平滑完成")
            return processed_pred

        except Exception as e:
            logger.warning(f"边缘感知平滑失败: {e}")
            return pred

    def _class_specific_processing(self, pred, probs):
        """类别特定后处理"""
        processed_pred = pred.copy()

        # 定义类别特定的处理策略
        class_strategies = {
            # 天空类别 - 通常在图像上部，应该是连续的大区域
            23: {'region_type': 'sky', 'min_size': 1000, 'position': 'top'},
            # 道路类别 - 通常在图像下部，应该是连续的
            28: {'region_type': 'road', 'min_size': 500, 'position': 'bottom'},
            # 建筑类别 - 通常是大的矩形区域
            22: {'region_type': 'building', 'min_size': 800, 'shape': 'rectangular'},
            # 人物类别 - 通常是垂直的椭圆形
            15: {'region_type': 'person', 'min_size': 100, 'shape': 'vertical'},
        }

        for class_id, strategy in class_strategies.items():
            if class_id >= self.num_classes:
                continue

            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() == 0:
                continue

            # 应用类别特定的处理
            if strategy['region_type'] == 'sky':
                processed_pred = self._process_sky_class(processed_pred, class_mask, class_id, probs)
            elif strategy['region_type'] == 'road':
                processed_pred = self._process_road_class(processed_pred, class_mask, class_id, probs)
            elif strategy['region_type'] == 'building':
                processed_pred = self._process_building_class(processed_pred, class_mask, class_id, probs)
            elif strategy['region_type'] == 'person':
                processed_pred = self._process_person_class(processed_pred, class_mask, class_id, probs)

        return processed_pred

    def _process_sky_class(self, pred, class_mask, class_id, probs):
        """处理天空类别"""
        h, w = pred.shape

        # 天空通常在图像上半部分
        upper_half = h // 2
        sky_in_upper = class_mask[:upper_half, :].sum()
        sky_in_lower = class_mask[upper_half:, :].sum()

        # 如果下半部分的天空太多，可能是误分类
        if sky_in_lower > sky_in_upper * 0.5:
            # 移除下半部分的小天空区域
            lower_mask = class_mask[upper_half:, :]
            num_labels, labels = cv2.connectedComponents(lower_mask)

            for label_id in range(1, num_labels):
                component_mask = (labels == label_id)
                if component_mask.sum() < 500:  # 移除小的天空区域
                    pred[upper_half:, :][component_mask] = 0

        return pred

    def _process_road_class(self, pred, class_mask, class_id, probs):
        """处理道路类别"""
        h, w = pred.shape

        # 道路通常在图像下半部分
        lower_half = h // 2
        road_in_lower = class_mask[lower_half:, :].sum()
        road_in_upper = class_mask[:lower_half, :].sum()

        # 如果上半部分的道路太多，可能是误分类
        if road_in_upper > road_in_lower * 0.3:
            # 移除上半部分的小道路区域
            upper_mask = class_mask[:lower_half, :]
            num_labels, labels = cv2.connectedComponents(upper_mask)

            for label_id in range(1, num_labels):
                component_mask = (labels == label_id)
                if component_mask.sum() < 300:
                    pred[:lower_half, :][component_mask] = 0

        return pred

    def _process_building_class(self, pred, class_mask, class_id, probs):
        """处理建筑类别"""
        # 建筑通常有较规整的形状，移除过于细长或不规则的区域
        num_labels, labels = cv2.connectedComponents(class_mask)

        for label_id in range(1, num_labels):
            component_mask = (labels == label_id)

            # 计算形状特征
            coords = np.where(component_mask)
            if len(coords[0]) < 100:
                continue

            y_min, y_max = coords[0].min(), coords[0].max()
            x_min, x_max = coords[1].min(), coords[1].max()

            height = y_max - y_min + 1
            width = x_max - x_min + 1
            area = component_mask.sum()

            # 计算紧凑度
            bbox_area = height * width
            compactness = area / bbox_area if bbox_area > 0 else 0

            # 移除过于细长或不紧凑的区域
            aspect_ratio = max(height, width) / min(height, width) if min(height, width) > 0 else float('inf')

            if aspect_ratio > 10 or compactness < 0.3:
                pred[component_mask] = 0

        return pred

    def _process_person_class(self, pred, class_mask, class_id, probs):
        """处理人物类别"""
        # 人物通常有特定的长宽比
        num_labels, labels = cv2.connectedComponents(class_mask)

        for label_id in range(1, num_labels):
            component_mask = (labels == label_id)

            if component_mask.sum() < 50:
                pred[component_mask] = 0
                continue

            # 计算形状特征
            coords = np.where(component_mask)
            y_min, y_max = coords[0].min(), coords[0].max()
            x_min, x_max = coords[1].min(), coords[1].max()

            height = y_max - y_min + 1
            width = x_max - x_min + 1

            # 人物通常高度大于宽度
            if height < width * 0.8:  # 太宽的可能不是人
                pred[component_mask] = 0

        return pred