#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn.functional as F
import numpy as np
import cv2
import yaml
import logging
from typing import Tuple, List, Optional, Dict
import time
from pathlib import Path

# 导入原始后处理模块
from enhanced_postprocessing import EnhancedPostProcessor

logger = logging.getLogger(__name__)

class ConfigurablePostProcessor(EnhancedPostProcessor):
    """可配置的增强后处理器"""
    
    def __init__(self, config_path: str = 'postprocessing_config.yaml', preset: str = 'balanced'):
        """
        初始化可配置后处理器
        
        Args:
            config_path: 配置文件路径
            preset: 预设配置名称 ('fast', 'balanced', 'high_quality')
        """
        # 加载配置
        self.config_data = self._load_config(config_path)
        
        # 获取基础配置
        basic_config = self.config_data.get('basic', {})
        num_classes = basic_config.get('num_classes', 29)
        device = basic_config.get('device', 'cuda')
        
        # 初始化父类
        super().__init__(num_classes=num_classes, device=device)
        
        # 应用预设配置
        self._apply_preset(preset)
        
        # 应用详细配置
        self._apply_detailed_config()
        
        logger.info(f"后处理器初始化完成，使用预设: {preset}")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"配置文件未找到: {config_path}，使用默认配置")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'basic': {'num_classes': 29, 'device': 'cuda'},
            'processing_switches': {
                'use_morphology': True,
                'use_connected_components': True,
                'use_confidence_filtering': True,
                'use_class_specific_processing': True,
            },
            'presets': {
                'balanced': {
                    'use_morphology': True,
                    'use_connected_components': True,
                    'use_confidence_filtering': True,
                    'use_class_specific_processing': True,
                }
            }
        }
    
    def _apply_preset(self, preset: str):
        """应用预设配置"""
        presets = self.config_data.get('presets', {})
        if preset in presets:
            preset_config = presets[preset]
            self.config.update(preset_config)
            logger.info(f"应用预设配置: {preset}")
        else:
            logger.warning(f"预设配置不存在: {preset}，使用默认配置")
    
    def _apply_detailed_config(self):
        """应用详细配置"""
        # 更新各种参数
        if 'tta_config' in self.config_data:
            self.tta_config.update(self.config_data['tta_config'])
        
        if 'crf_config' in self.config_data:
            self.crf_config.update(self.config_data['crf_config'])
        
        if 'morph_params' in self.config_data:
            self.morph_params.update(self.config_data['morph_params'])
        
        if 'cc_params' in self.config_data:
            self.cc_params.update(self.config_data['cc_params'])
        
        if 'conf_params' in self.config_data:
            self.conf_params.update(self.config_data['conf_params'])
    
    def get_optimized_config_for_model(self, model_performance: Dict) -> str:
        """根据模型性能推荐最优配置"""
        miou = model_performance.get('miou', 0.0)
        inference_time = model_performance.get('inference_time', 1.0)
        
        # 根据性能选择配置
        if miou < 0.3:
            # 低性能模型，使用高质量后处理
            return 'high_quality'
        elif miou < 0.45:
            # 中等性能模型，使用平衡配置
            return 'balanced'
        else:
            # 高性能模型，使用快速配置
            return 'fast'
    
    def benchmark_configurations(self, test_data: List[Tuple], num_samples: int = 10) -> Dict:
        """基准测试不同配置的性能"""
        results = {}
        presets = ['fast', 'balanced', 'high_quality']
        
        for preset in presets:
            logger.info(f"测试配置: {preset}")
            
            # 临时切换配置
            original_config = self.config.copy()
            self._apply_preset(preset)
            
            # 测试性能
            total_time = 0
            total_improvement = 0
            
            for i, (logits, original_image) in enumerate(test_data[:num_samples]):
                start_time = time.time()
                
                # 原始预测
                original_pred = torch.argmax(logits, dim=0).cpu().numpy()
                
                # 后处理预测
                processed_pred, _, _ = self.process_prediction(
                    logits.unsqueeze(0), original_image=original_image
                )
                
                processing_time = time.time() - start_time
                total_time += processing_time
                
                # 计算改善程度（这里用变化像素数作为代理指标）
                improvement = np.sum(processed_pred != original_pred) / original_pred.size
                total_improvement += improvement
            
            # 恢复原配置
            self.config = original_config
            
            # 记录结果
            results[preset] = {
                'avg_processing_time': total_time / num_samples,
                'avg_improvement': total_improvement / num_samples,
                'efficiency_score': (total_improvement / num_samples) / (total_time / num_samples)
            }
        
        return results
    
    def auto_optimize_for_dataset(self, validation_data: List[Tuple], 
                                target_miou: float = 0.5) -> str:
        """为数据集自动优化配置"""
        logger.info("开始自动优化后处理配置...")
        
        # 基准测试
        benchmark_results = self.benchmark_configurations(validation_data)
        
        # 选择最佳配置
        best_preset = 'balanced'
        best_score = 0
        
        for preset, metrics in benchmark_results.items():
            # 综合评分：改善程度 * 效率
            score = metrics['avg_improvement'] * metrics['efficiency_score']
            
            if score > best_score:
                best_score = score
                best_preset = preset
        
        logger.info(f"自动优化完成，推荐配置: {best_preset}")
        logger.info(f"预期改善: {benchmark_results[best_preset]['avg_improvement']:.3f}")
        logger.info(f"平均处理时间: {benchmark_results[best_preset]['avg_processing_time']:.3f}s")
        
        return best_preset
    
    def save_optimized_config(self, preset: str, output_path: str = 'optimized_postprocessing_config.yaml'):
        """保存优化后的配置"""
        optimized_config = self.config_data.copy()
        
        # 更新默认配置为优化后的预设
        if preset in optimized_config.get('presets', {}):
            optimized_config['processing_switches'] = optimized_config['presets'][preset]
        
        # 添加优化信息
        optimized_config['optimization_info'] = {
            'optimized_preset': preset,
            'optimization_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'recommended_for': 'current_model_performance'
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(optimized_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"优化配置已保存到: {output_path}")

def create_postprocessor(config_path: str = 'postprocessing_config.yaml', 
                        preset: str = 'balanced') -> ConfigurablePostProcessor:
    """创建后处理器的工厂函数"""
    return ConfigurablePostProcessor(config_path=config_path, preset=preset)

def quick_test_postprocessing():
    """快速测试后处理功能"""
    print("🧪 快速测试后处理功能")
    
    # 创建测试数据
    logits = torch.randn(29, 256, 256)
    image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    
    # 测试不同配置
    configs = ['fast', 'balanced', 'high_quality']
    
    for config in configs:
        print(f"\n测试配置: {config}")
        
        try:
            processor = create_postprocessor(preset=config)
            
            start_time = time.time()
            processed_pred, confidence_map, info = processor.process_prediction(
                logits.unsqueeze(0), original_image=image
            )
            processing_time = time.time() - start_time
            
            print(f"  ✅ 成功 - 处理时间: {processing_time:.3f}s")
            print(f"  📊 输出形状: {processed_pred.shape}")
            
        except Exception as e:
            print(f"  ❌ 失败: {e}")

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行快速测试
    quick_test_postprocessing()
