#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新增图像评估工具
评估D:\add_90_pictures中70张新增图像的内容和质量
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
from typing import Dict, List, Tuple
import json
from collections import Counter
import hashlib

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewImageEvaluator:
    """新增图像评估器"""
    
    def __init__(self, image_dir: str = r"D:\add_90_pictures"):
        self.image_dir = Path(image_dir)
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 目标类别 (基于之前的分析)
        self.target_classes = {
            'miscellaneous_elements': '杂项元素 (垃圾桶、装饰品、小型设施等)',
            'planter': '花坛/种植箱',
            'shrub': '灌木',
            'pole': '杆子/柱子',
            'tree_pit': '树坑'
        }
        
        # 评估结果
        self.evaluation_results = {}
    
    def get_image_files(self) -> List[Path]:
        """获取所有图像文件"""
        if not self.image_dir.exists():
            logger.error(f"❌ 目录不存在: {self.image_dir}")
            return []
        
        image_files = []
        for file_path in self.image_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.image_extensions:
                image_files.append(file_path)
        
        return sorted(image_files)
    
    def analyze_image_quality(self, image_files: List[Path]) -> Dict:
        """分析图像质量"""
        logger.info("🔍 分析图像质量...")
        
        quality_metrics = {
            'total_images': len(image_files),
            'resolution_analysis': {},
            'brightness_analysis': {},
            'sharpness_analysis': {},
            'color_analysis': {},
            'file_size_analysis': {},
            'quality_scores': [],
            'sample_analysis': []
        }
        
        resolutions = []
        brightness_values = []
        sharpness_values = []
        file_sizes = []
        
        for i, img_path in enumerate(image_files):
            try:
                # 读取图像
                image = cv2.imread(str(img_path))
                if image is None:
                    continue
                
                height, width, channels = image.shape
                resolutions.append((width, height))
                
                # 亮度分析
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                brightness = np.mean(gray)
                brightness_values.append(brightness)
                
                # 清晰度分析 (拉普拉斯方差)
                laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
                sharpness_values.append(laplacian_var)
                
                # 文件大小
                file_size = img_path.stat().st_size / (1024 * 1024)  # MB
                file_sizes.append(file_size)
                
                # 计算质量评分
                quality_score = self._calculate_quality_score(brightness, laplacian_var, file_size)
                quality_metrics['quality_scores'].append(quality_score)
                
                # 保存前5张图像的详细分析
                if i < 5:
                    sample_info = {
                        'filename': img_path.name,
                        'resolution': f"{width}x{height}",
                        'brightness': round(brightness, 2),
                        'sharpness': round(laplacian_var, 2),
                        'file_size_mb': round(file_size, 2),
                        'quality_score': round(quality_score, 2)
                    }
                    quality_metrics['sample_analysis'].append(sample_info)
                
            except Exception as e:
                logger.warning(f"⚠️ 分析图像失败 {img_path}: {e}")
        
        # 统计分析
        if resolutions:
            unique_resolutions = list(set(resolutions))
            quality_metrics['resolution_analysis'] = {
                'unique_resolutions': len(unique_resolutions),
                'most_common': Counter(resolutions).most_common(3),
                'consistent': len(unique_resolutions) == 1
            }
        
        if brightness_values:
            quality_metrics['brightness_analysis'] = {
                'mean': np.mean(brightness_values),
                'std': np.std(brightness_values),
                'min': np.min(brightness_values),
                'max': np.max(brightness_values),
                'well_exposed': sum(50 <= b <= 200 for b in brightness_values)
            }
        
        if sharpness_values:
            quality_metrics['sharpness_analysis'] = {
                'mean': np.mean(sharpness_values),
                'std': np.std(sharpness_values),
                'min': np.min(sharpness_values),
                'max': np.max(sharpness_values),
                'sharp_images': sum(s > 100 for s in sharpness_values)
            }
        
        if file_sizes:
            quality_metrics['file_size_analysis'] = {
                'mean_mb': np.mean(file_sizes),
                'total_mb': np.sum(file_sizes),
                'min_mb': np.min(file_sizes),
                'max_mb': np.max(file_sizes),
                'consistent_size': np.std(file_sizes) < 2.0
            }
        
        return quality_metrics
    
    def _calculate_quality_score(self, brightness: float, sharpness: float, file_size: float) -> float:
        """计算图像质量评分 (0-10)"""
        score = 0
        
        # 亮度评分 (0-3分)
        if 80 <= brightness <= 180:
            score += 3
        elif 50 <= brightness <= 220:
            score += 2
        elif 30 <= brightness <= 250:
            score += 1
        
        # 清晰度评分 (0-4分)
        if sharpness > 500:
            score += 4
        elif sharpness > 200:
            score += 3
        elif sharpness > 100:
            score += 2
        elif sharpness > 50:
            score += 1
        
        # 文件大小评分 (0-3分)
        if 3 <= file_size <= 10:
            score += 3
        elif 1 <= file_size <= 15:
            score += 2
        elif 0.5 <= file_size <= 20:
            score += 1
        
        return score
    
    def analyze_content_diversity(self, image_files: List[Path]) -> Dict:
        """分析内容多样性"""
        logger.info("🔍 分析内容多样性...")
        
        diversity_metrics = {
            'filename_analysis': {},
            'visual_diversity': {},
            'potential_target_classes': {},
            'recommendations': []
        }
        
        # 文件名分析
        filenames = [f.stem for f in image_files]
        filename_numbers = []
        for name in filenames:
            try:
                num = int(name)
                filename_numbers.append(num)
            except ValueError:
                pass
        
        if filename_numbers:
            diversity_metrics['filename_analysis'] = {
                'numeric_names': len(filename_numbers),
                'number_range': (min(filename_numbers), max(filename_numbers)),
                'sequential': self._is_mostly_sequential(filename_numbers),
                'gaps': self._find_gaps(filename_numbers)
            }
        
        # 视觉多样性分析 (基于颜色直方图)
        color_histograms = []
        for img_path in image_files[:20]:  # 分析前20张
            try:
                image = cv2.imread(str(img_path))
                if image is not None:
                    # 计算颜色直方图
                    hist = cv2.calcHist([image], [0, 1, 2], None, [8, 8, 8], [0, 256, 0, 256, 0, 256])
                    color_histograms.append(hist.flatten())
            except Exception:
                continue
        
        if len(color_histograms) > 1:
            # 计算直方图之间的相似性
            similarities = []
            for i in range(len(color_histograms)):
                for j in range(i+1, len(color_histograms)):
                    similarity = cv2.compareHist(color_histograms[i], color_histograms[j], cv2.HISTCMP_CORREL)
                    similarities.append(similarity)
            
            diversity_metrics['visual_diversity'] = {
                'avg_similarity': np.mean(similarities),
                'diversity_score': 1 - np.mean(similarities),  # 越不相似越多样
                'highly_similar_pairs': sum(s > 0.9 for s in similarities)
            }
        
        # 基于文件名推测可能的目标类别
        self._analyze_potential_classes(filenames, diversity_metrics)
        
        return diversity_metrics
    
    def _is_mostly_sequential(self, numbers: List[int]) -> bool:
        """检查数字是否大致连续"""
        if len(numbers) < 2:
            return True
        
        sorted_nums = sorted(numbers)
        gaps = [sorted_nums[i+1] - sorted_nums[i] for i in range(len(sorted_nums)-1)]
        avg_gap = np.mean(gaps)
        return avg_gap <= 2.0
    
    def _find_gaps(self, numbers: List[int]) -> List[Tuple[int, int]]:
        """找出数字序列中的间隙"""
        if len(numbers) < 2:
            return []
        
        sorted_nums = sorted(set(numbers))
        gaps = []
        for i in range(len(sorted_nums)-1):
            if sorted_nums[i+1] - sorted_nums[i] > 1:
                gaps.append((sorted_nums[i], sorted_nums[i+1]))
        
        return gaps[:5]  # 只返回前5个间隙
    
    def _analyze_potential_classes(self, filenames: List[str], diversity_metrics: Dict):
        """分析可能包含的目标类别"""
        # 这里可以基于文件名模式、数量分布等推测
        # 由于是手动标注，假设用户有针对性地选择了包含目标类别的图像
        
        total_images = len(filenames)
        
        # 基于我们的目标需求估算
        potential_classes = {
            'miscellaneous_elements': {
                'estimated_images': min(25, total_images // 3),
                'confidence': 'medium',
                'description': '预计包含杂项元素的图像'
            },
            'planter': {
                'estimated_images': min(20, total_images // 4),
                'confidence': 'medium', 
                'description': '预计包含花坛/种植箱的图像'
            },
            'other_targets': {
                'estimated_images': total_images - 45,
                'confidence': 'low',
                'description': '可能包含其他目标类别的图像'
            }
        }
        
        diversity_metrics['potential_target_classes'] = potential_classes
    
    def generate_recommendations(self, quality_metrics: Dict, diversity_metrics: Dict) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 质量建议
        if quality_metrics.get('quality_scores'):
            avg_quality = np.mean(quality_metrics['quality_scores'])
            if avg_quality >= 8:
                recommendations.append("✅ 图像质量优秀，适合直接用于训练")
            elif avg_quality >= 6:
                recommendations.append("✅ 图像质量良好，可以用于训练")
            elif avg_quality >= 4:
                recommendations.append("⚠️ 图像质量中等，建议检查亮度和清晰度")
            else:
                recommendations.append("❌ 图像质量较差，建议重新拍摄或处理")
        
        # 分辨率建议
        if quality_metrics.get('resolution_analysis', {}).get('consistent'):
            recommendations.append("✅ 图像分辨率一致，有利于训练")
        else:
            recommendations.append("⚠️ 图像分辨率不一致，建议统一尺寸")
        
        # 数量建议
        total_images = quality_metrics.get('total_images', 0)
        if total_images >= 50:
            recommendations.append(f"✅ 图像数量充足 ({total_images}张)，可以有效补充数据集")
        elif total_images >= 30:
            recommendations.append(f"✅ 图像数量适中 ({total_images}张)，可以补充关键类别")
        else:
            recommendations.append(f"⚠️ 图像数量较少 ({total_images}张)，建议增加更多样本")
        
        # 多样性建议
        if diversity_metrics.get('visual_diversity', {}).get('diversity_score', 0) > 0.3:
            recommendations.append("✅ 图像内容多样性良好")
        else:
            recommendations.append("⚠️ 图像内容相似度较高，建议增加场景多样性")
        
        return recommendations
    
    def run_complete_evaluation(self) -> Dict:
        """运行完整评估"""
        logger.info("🚀 开始评估新增图像")
        
        # 1. 获取图像文件
        image_files = self.get_image_files()
        
        if not image_files:
            logger.error("❌ 没有找到图像文件")
            return {}
        
        # 2. 分析图像质量
        quality_metrics = self.analyze_image_quality(image_files)
        
        # 3. 分析内容多样性
        diversity_metrics = self.analyze_content_diversity(image_files)
        
        # 4. 生成建议
        recommendations = self.generate_recommendations(quality_metrics, diversity_metrics)
        
        # 5. 汇总结果
        self.evaluation_results = {
            'directory': str(self.image_dir),
            'evaluation_time': datetime.now().isoformat(),
            'summary': {
                'total_images': len(image_files),
                'avg_quality_score': np.mean(quality_metrics.get('quality_scores', [0])),
                'total_size_mb': quality_metrics.get('file_size_analysis', {}).get('total_mb', 0)
            },
            'quality_metrics': quality_metrics,
            'diversity_metrics': diversity_metrics,
            'recommendations': recommendations,
            'next_steps': self._generate_next_steps(quality_metrics, diversity_metrics)
        }
        
        return self.evaluation_results
    
    def _generate_next_steps(self, quality_metrics: Dict, diversity_metrics: Dict) -> List[str]:
        """生成下一步行动建议"""
        next_steps = []
        
        total_images = quality_metrics.get('total_images', 0)
        
        if total_images > 0:
            next_steps.append(f"1. 开始对这{total_images}张图像进行语义分割标注")
            next_steps.append("2. 重点标注类别28 (miscellaneous elements) 和类别27 (planter)")
            next_steps.append("3. 使用ISAT工具进行精确的像素级标注")
            next_steps.append("4. 确保每个目标类别在图像中占5-10%的像素比例")
            next_steps.append("5. 完成标注后，将新数据加入训练集重新训练模型")
            next_steps.append("6. 验证模型性能提升，预期mIoU提升10-15%")
        
        return next_steps

def main():
    """主函数"""
    print("📊 新增图像评估工具")
    print("=" * 60)
    
    # 检查目录
    image_dir = r"D:\add_90_pictures"
    
    if not os.path.exists(image_dir):
        print(f"❌ 目录不存在: {image_dir}")
        return
    
    print(f"📁 评估目录: {image_dir}")
    
    # 创建评估器
    evaluator = NewImageEvaluator(image_dir)
    
    # 执行评估
    results = evaluator.run_complete_evaluation()
    
    if not results:
        print("❌ 评估失败")
        return
    
    # 显示结果
    summary = results['summary']
    print(f"\n📊 评估结果摘要:")
    print(f"   图像总数: {summary['total_images']} 张")
    print(f"   平均质量评分: {summary['avg_quality_score']:.2f}/10")
    print(f"   总文件大小: {summary['total_size_mb']:.2f} MB")
    
    # 显示质量分析
    quality = results['quality_metrics']
    if 'brightness_analysis' in quality:
        brightness = quality['brightness_analysis']
        print(f"\n📐 图像质量分析:")
        print(f"   分辨率一致性: {'✅ 一致' if quality.get('resolution_analysis', {}).get('consistent') else '⚠️ 不一致'}")
        print(f"   曝光良好图像: {brightness.get('well_exposed', 0)}/{summary['total_images']} 张")
        print(f"   清晰图像: {quality.get('sharpness_analysis', {}).get('sharp_images', 0)}/{summary['total_images']} 张")
    
    # 显示样本分析
    if 'sample_analysis' in quality and quality['sample_analysis']:
        print(f"\n📋 样本分析 (前5张):")
        for sample in quality['sample_analysis']:
            print(f"   {sample['filename']}: {sample['resolution']}, 质量评分 {sample['quality_score']}/10")
    
    # 显示建议
    print(f"\n💡 建议:")
    for rec in results['recommendations']:
        print(f"   {rec}")
    
    # 显示下一步
    print(f"\n🎯 下一步行动:")
    for step in results['next_steps']:
        print(f"   {step}")
    
    # 保存详细报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = f"new_image_evaluation_{timestamp}.json"
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n📄 详细评估报告: {report_path}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")

if __name__ == "__main__":
    main()
