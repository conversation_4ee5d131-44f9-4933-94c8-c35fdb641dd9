#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn.functional as F
import numpy as np
import cv2
import os
import glob
import logging
from datetime import datetime
import json
import time
from typing import Dict, List, Tuple

# 导入后处理模块
from enhanced_postprocessing_fixed import create_postprocessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PostProcessingEvaluator:
    """后处理效果评估器 - 使用模拟预测"""
    
    def __init__(self, num_classes=29):
        self.num_classes = num_classes
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建不同配置的后处理器
        self.processors = {
            'fast': create_postprocessor(preset='fast'),
            'balanced': create_postprocessor(preset='balanced'),
            'high_quality': create_postprocessor(preset='high_quality')
        }
        
        # 评估结果
        self.results = {}
    
    def create_synthetic_prediction(self, height=512, width=512) -> <PERSON><PERSON>[torch.Tensor, np.ndarray, np.ndarray]:
        """创建合成的模型预测数据"""
        # 创建模拟的logits
        logits = torch.randn(self.num_classes, height, width)
        
        # 创建一些结构化的预测区域
        # 背景区域
        logits[0, :100, :] = 3.0
        
        # 建筑区域 (class 22)
        logits[22, 100:300, 100:400] = 2.5
        
        # 道路区域 (class 28)  
        logits[28, 400:, :] = 2.0
        
        # 天空区域 (class 23)
        logits[23, :150, :] = 1.8
        
        # 人物区域 (class 15)
        logits[15, 200:350, 200:250] = 1.5
        
        # 添加噪声
        noise = torch.randn_like(logits) * 0.5
        logits += noise
        
        # 创建对应的"真实"标签（基于logits但更干净）
        clean_logits = torch.zeros_like(logits)
        clean_logits[0, :100, :] = 5.0
        clean_logits[22, 100:300, 100:400] = 5.0
        clean_logits[28, 400:, :] = 5.0
        clean_logits[23, :150, :] = 5.0
        clean_logits[15, 200:350, 200:250] = 5.0
        
        ground_truth = torch.argmax(clean_logits, dim=0).numpy()
        
        # 创建对应的图像
        image = np.zeros((height, width, 3), dtype=np.uint8)
        image[:100, :] = [100, 100, 100]  # 背景 - 灰色
        image[100:300, 100:400] = [150, 150, 150]  # 建筑 - 浅灰
        image[400:, :] = [50, 50, 50]  # 道路 - 深灰
        image[:150, :] = [135, 206, 235]  # 天空 - 蓝色
        image[200:350, 200:250] = [255, 220, 177]  # 人物 - 肤色
        
        return logits, image, ground_truth
    
    def calculate_miou(self, pred: np.ndarray, target: np.ndarray) -> float:
        """计算mIoU"""
        miou = 0
        valid_classes = 0
        
        for class_id in range(self.num_classes):
            pred_mask = (pred == class_id)
            target_mask = (target == class_id)
            
            intersection = np.logical_and(pred_mask, target_mask).sum()
            union = np.logical_or(pred_mask, target_mask).sum()
            
            if union > 0:
                iou = intersection / union
                miou += iou
                valid_classes += 1
        
        return miou / valid_classes if valid_classes > 0 else 0
    
    def evaluate_postprocessing_impact(self, num_samples: int = 20) -> Dict:
        """评估后处理对性能的影响"""
        logger.info(f"开始评估后处理效果，样本数: {num_samples}")
        
        results = {
            'baseline': {'miou_scores': [], 'processing_times': []},
            'fast': {'miou_scores': [], 'processing_times': []},
            'balanced': {'miou_scores': [], 'processing_times': []},
            'high_quality': {'miou_scores': [], 'processing_times': []}
        }
        
        for i in range(num_samples):
            # 创建测试数据
            logits, image, ground_truth = self.create_synthetic_prediction()
            
            # 基线预测（无后处理）
            baseline_pred = torch.argmax(logits, dim=0).numpy()
            baseline_miou = self.calculate_miou(baseline_pred, ground_truth)
            results['baseline']['miou_scores'].append(baseline_miou)
            results['baseline']['processing_times'].append(0.0)  # 无后处理时间
            
            # 测试不同后处理配置
            for config_name, processor in self.processors.items():
                start_time = time.time()
                
                try:
                    processed_pred, _, _ = processor.process_prediction(
                        logits.unsqueeze(0),
                        original_image=image,
                        original_size=None
                    )
                    
                    processing_time = time.time() - start_time
                    processed_miou = self.calculate_miou(processed_pred, ground_truth)
                    
                    results[config_name]['miou_scores'].append(processed_miou)
                    results[config_name]['processing_times'].append(processing_time)
                    
                except Exception as e:
                    logger.warning(f"配置 {config_name} 处理失败: {e}")
                    results[config_name]['miou_scores'].append(baseline_miou)
                    results[config_name]['processing_times'].append(0.0)
            
            if (i + 1) % 5 == 0:
                logger.info(f"已处理 {i+1}/{num_samples} 个样本")
        
        # 计算统计信息
        summary = {}
        for config_name, data in results.items():
            miou_scores = data['miou_scores']
            processing_times = data['processing_times']
            
            summary[config_name] = {
                'avg_miou': np.mean(miou_scores),
                'std_miou': np.std(miou_scores),
                'avg_processing_time': np.mean(processing_times),
                'std_processing_time': np.std(processing_times),
                'miou_improvement': np.mean(miou_scores) - np.mean(results['baseline']['miou_scores']),
                'samples': len(miou_scores)
            }
        
        return summary
    
    def generate_report(self, results: Dict, output_path: str = None) -> str:
        """生成评估报告"""
        if output_path is None:
            output_path = f"postprocessing_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 添加元数据
        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'evaluation_type': 'postprocessing_impact_analysis',
            'num_classes': self.num_classes,
            'device': str(self.device),
            'results': results
        }
        
        # 保存JSON报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 生成文本摘要
        summary_text = self._generate_text_summary(results)
        
        # 保存文本摘要
        text_path = output_path.replace('.json', '_summary.txt')
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(summary_text)
        
        logger.info(f"评估报告已保存: {output_path}")
        logger.info(f"文本摘要已保存: {text_path}")
        
        return summary_text
    
    def _generate_text_summary(self, results: Dict) -> str:
        """生成文本摘要"""
        baseline_miou = results['baseline']['avg_miou']
        
        summary = f"""
# 🔍 后处理效果评估报告

## 📊 总体结果

**基线性能 (无后处理)**:
- 平均mIoU: {baseline_miou:.4f}
- 处理时间: 0.000s

**后处理配置对比**:

"""
        
        configs = ['fast', 'balanced', 'high_quality']
        for config in configs:
            if config in results:
                data = results[config]
                improvement = data['miou_improvement']
                improvement_pct = (improvement / baseline_miou * 100) if baseline_miou > 0 else 0
                
                summary += f"""
### {config.upper()} 配置
- 平均mIoU: {data['avg_miou']:.4f}
- mIoU提升: {improvement:+.4f} ({improvement_pct:+.1f}%)
- 平均处理时间: {data['avg_processing_time']:.3f}s
- 标准差: ±{data['std_miou']:.4f}

"""
        
        # 找出最佳配置
        best_config = 'baseline'
        best_miou = baseline_miou
        
        for config in configs:
            if config in results and results[config]['avg_miou'] > best_miou:
                best_miou = results[config]['avg_miou']
                best_config = config
        
        summary += f"""
## 🏆 推荐配置

**最佳配置**: {best_config.upper()}
- 最高mIoU: {best_miou:.4f}
- 相比基线提升: {best_miou - baseline_miou:+.4f}

## 💡 建议

"""
        
        if best_config != 'baseline':
            summary += f"建议使用 {best_config} 配置进行后处理，可以显著提升模型性能。\n"
        else:
            summary += "当前后处理配置未能显著提升性能，建议调整参数或使用其他优化策略。\n"
        
        summary += f"""
## ⚡ 性能分析

- **Fast配置**: 速度最快，适合实时应用
- **Balanced配置**: 平衡速度和效果，推荐日常使用  
- **High Quality配置**: 效果最好，适合离线处理

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return summary

def main():
    """主函数"""
    print("🚀 开始后处理效果评估")
    print("=" * 60)
    
    # 创建评估器
    evaluator = PostProcessingEvaluator()
    
    # 运行评估
    results = evaluator.evaluate_postprocessing_impact(num_samples=30)
    
    # 生成报告
    summary = evaluator.generate_report(results)
    
    # 打印摘要
    print(summary)
    
    # 简单的建议
    baseline_miou = results['baseline']['avg_miou']
    best_improvement = 0
    best_config = 'baseline'
    
    for config in ['fast', 'balanced', 'high_quality']:
        if config in results:
            improvement = results[config]['miou_improvement']
            if improvement > best_improvement:
                best_improvement = improvement
                best_config = config
    
    if best_improvement > 0.01:  # 提升超过1%
        print(f"\n🎉 后处理显著提升了性能！")
        print(f"推荐使用 {best_config} 配置，可提升 {best_improvement:.4f} mIoU")
        print(f"这相当于 {best_improvement/baseline_miou*100:.1f}% 的相对提升")
    else:
        print(f"\n⚠️ 后处理提升有限，可能需要:")
        print(f"1. 调整后处理参数")
        print(f"2. 使用更强的模型架构")
        print(f"3. 改进训练策略")

if __name__ == "__main__":
    main()
