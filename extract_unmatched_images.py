#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
提取未匹配图像工具
将D:\1a_taohuacun中不在D:\123pictures\images中的图片复制到新文件夹
"""

import os
import shutil
from pathlib import Path
import logging
from datetime import datetime
from typing import Set, List
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnmatchedImageExtractor:
    """未匹配图像提取器"""
    
    def __init__(self, 
                 source_dir: str = r"D:\1a_taohuacun", 
                 matched_dir: str = r"D:\123pictures\images",
                 output_dir: str = r"D:\unmatched_images"):
        self.source_dir = Path(source_dir)
        self.matched_dir = Path(matched_dir)
        self.output_dir = Path(output_dir)
        
        # 支持的图像格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 统计信息
        self.total_source_images = 0
        self.matched_images = 0
        self.unmatched_images = 0
        self.copied_images = 0
        
    def get_image_files(self, directory: Path) -> Set[str]:
        """获取目录中的所有图像文件名"""
        image_files = set()
        
        if not directory.exists():
            logger.warning(f"目录不存在: {directory}")
            return image_files
        
        for file_path in directory.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.image_extensions:
                image_files.add(file_path.name)
        
        return image_files
    
    def create_output_directory(self) -> bool:
        """创建输出目录"""
        try:
            if self.output_dir.exists():
                # 如果目录已存在，创建带时间戳的新目录
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                self.output_dir = Path(str(self.output_dir) + f"_{timestamp}")
            
            self.output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 输出目录创建成功: {self.output_dir}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建输出目录失败: {e}")
            return False
    
    def copy_unmatched_images(self, unmatched_files: Set[str]) -> int:
        """复制未匹配的图像文件"""
        copied_count = 0
        failed_count = 0
        
        logger.info(f"🚀 开始复制 {len(unmatched_files)} 个未匹配的图像文件...")
        
        for filename in unmatched_files:
            try:
                source_path = self.source_dir / filename
                target_path = self.output_dir / filename
                
                # 检查源文件是否存在
                if not source_path.exists():
                    logger.warning(f"⚠️ 源文件不存在: {source_path}")
                    failed_count += 1
                    continue
                
                # 如果目标文件已存在，添加序号
                if target_path.exists():
                    base_name = target_path.stem
                    extension = target_path.suffix
                    counter = 1
                    while target_path.exists():
                        target_path = self.output_dir / f"{base_name}_{counter}{extension}"
                        counter += 1
                
                # 复制文件
                shutil.copy2(source_path, target_path)
                copied_count += 1
                
                if copied_count % 50 == 0:  # 每50个文件报告一次进度
                    logger.info(f"📊 已复制 {copied_count}/{len(unmatched_files)} 个文件")
                
            except Exception as e:
                logger.error(f"❌ 复制文件失败 {filename}: {e}")
                failed_count += 1
        
        logger.info(f"📊 复制完成: {copied_count} 成功, {failed_count} 失败")
        return copied_count
    
    def generate_report(self, unmatched_files: Set[str]) -> str:
        """生成提取报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.output_dir / f"unmatched_images_report_{timestamp}.json"
        
        report_data = {
            'timestamp': timestamp,
            'source_directory': str(self.source_dir),
            'matched_directory': str(self.matched_dir),
            'output_directory': str(self.output_dir),
            'statistics': {
                'total_source_images': self.total_source_images,
                'matched_images': self.matched_images,
                'unmatched_images': self.unmatched_images,
                'copied_images': self.copied_images
            },
            'unmatched_files': sorted(list(unmatched_files))
        }
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 报告已保存: {report_path}")
            return str(report_path)
            
        except Exception as e:
            logger.error(f"❌ 保存报告失败: {e}")
            return ""
    
    def extract_unmatched_images(self) -> bool:
        """提取未匹配的图像文件"""
        logger.info("🚀 开始提取未匹配的图像文件")
        
        # 1. 获取源目录中的所有图像文件
        logger.info("🔍 扫描源目录中的图像文件...")
        source_files = self.get_image_files(self.source_dir)
        self.total_source_images = len(source_files)
        logger.info(f"✅ 源目录找到 {self.total_source_images} 张图像")
        
        if self.total_source_images == 0:
            logger.error("❌ 源目录中没有找到图像文件")
            return False
        
        # 2. 获取已匹配目录中的所有图像文件
        logger.info("🔍 扫描已匹配目录中的图像文件...")
        matched_files = self.get_image_files(self.matched_dir)
        self.matched_images = len(matched_files)
        logger.info(f"✅ 已匹配目录找到 {self.matched_images} 张图像")
        
        # 3. 找出未匹配的文件
        unmatched_files = source_files - matched_files
        self.unmatched_images = len(unmatched_files)
        
        logger.info(f"📊 统计结果:")
        logger.info(f"   源目录图像总数: {self.total_source_images}")
        logger.info(f"   已匹配图像数量: {self.matched_images}")
        logger.info(f"   未匹配图像数量: {self.unmatched_images}")
        
        if self.unmatched_images == 0:
            logger.info("🎉 所有图像都已匹配，无需提取")
            return True
        
        # 4. 创建输出目录
        if not self.create_output_directory():
            return False
        
        # 5. 复制未匹配的图像文件
        self.copied_images = self.copy_unmatched_images(unmatched_files)
        
        # 6. 生成报告
        self.generate_report(unmatched_files)
        
        # 7. 显示最终结果
        logger.info(f"🎉 提取完成!")
        logger.info(f"📁 输出目录: {self.output_dir}")
        logger.info(f"📊 最终统计:")
        logger.info(f"   未匹配图像: {self.unmatched_images} 张")
        logger.info(f"   成功复制: {self.copied_images} 张")
        logger.info(f"   复制成功率: {(self.copied_images/self.unmatched_images*100):.1f}%")
        
        return self.copied_images > 0
    
    def show_sample_files(self, unmatched_files: Set[str], sample_size: int = 10):
        """显示未匹配文件的样本"""
        if not unmatched_files:
            return
        
        sample_files = sorted(list(unmatched_files))[:sample_size]
        logger.info(f"📋 未匹配文件样本 (前{len(sample_files)}个):")
        for i, filename in enumerate(sample_files, 1):
            logger.info(f"   {i:2d}. {filename}")
        
        if len(unmatched_files) > sample_size:
            logger.info(f"   ... 还有 {len(unmatched_files) - sample_size} 个文件")

def main():
    """主函数"""
    print("📁 未匹配图像提取工具")
    print("=" * 60)
    
    # 检查目录是否存在
    source_dir = r"D:\1a_taohuacun"
    matched_dir = r"D:\123pictures\images"
    output_dir = r"D:\unmatched_images"
    
    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        return
    
    if not os.path.exists(matched_dir):
        print(f"❌ 已匹配目录不存在: {matched_dir}")
        return
    
    print(f"📁 源目录: {source_dir}")
    print(f"📁 已匹配目录: {matched_dir}")
    print(f"📁 输出目录: {output_dir}")
    
    # 创建提取器
    extractor = UnmatchedImageExtractor(source_dir, matched_dir, output_dir)
    
    # 执行提取
    success = extractor.extract_unmatched_images()
    
    if success:
        print("\n🎉 未匹配图像提取完成！")
        print(f"📁 请查看输出目录: {extractor.output_dir}")
        
        # 显示一些未匹配文件的样本
        source_files = extractor.get_image_files(extractor.source_dir)
        matched_files = extractor.get_image_files(extractor.matched_dir)
        unmatched_files = source_files - matched_files
        extractor.show_sample_files(unmatched_files)
        
    else:
        print("❌ 未匹配图像提取失败")

if __name__ == "__main__":
    main()
