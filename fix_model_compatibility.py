#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模型兼容性修复器
解决不同模型架构的加载问题
"""

import torch
import torch.nn as nn
import os
import sys
import logging
from typing import Dict, Any, Optional
import json

# 添加路径
sys.path.append('UNet_Demo/UNet_Demo')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelCompatibilityFixer:
    """模型兼容性修复器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def analyze_model_structure(self, model_path: str) -> Dict[str, Any]:
        """分析模型结构"""
        try:
            checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
            
            analysis = {
                'checkpoint_keys': list(checkpoint.keys()),
                'model_info': {},
                'state_dict_info': {},
                'compatibility_issues': []
            }
            
            # 分析checkpoint结构
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
                analysis['has_model_state_dict'] = True
            else:
                state_dict = checkpoint
                analysis['has_model_state_dict'] = False
                
            # 分析state_dict
            state_dict_keys = list(state_dict.keys())
            analysis['state_dict_info'] = {
                'total_keys': len(state_dict_keys),
                'sample_keys': state_dict_keys[:20],
                'key_patterns': self._analyze_key_patterns(state_dict_keys)
            }
            
            # 检查模型信息
            if 'epoch' in checkpoint:
                analysis['model_info']['epoch'] = checkpoint['epoch']
            if 'best_miou' in checkpoint:
                analysis['model_info']['best_miou'] = checkpoint['best_miou']
            if 'stage' in checkpoint:
                analysis['model_info']['stage'] = checkpoint['stage']
                
            # 检测兼容性问题
            analysis['compatibility_issues'] = self._detect_compatibility_issues(state_dict_keys)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析模型结构失败: {e}")
            return {'error': str(e)}
    
    def _analyze_key_patterns(self, keys: list) -> Dict[str, int]:
        """分析键名模式"""
        patterns = {}
        
        for key in keys:
            # 提取模式
            if key.startswith('model.'):
                pattern = 'model_prefix'
            elif key.startswith('encoder.'):
                pattern = 'encoder_prefix'
            elif key.startswith('decoder.'):
                pattern = 'decoder_prefix'
            elif 'attention' in key:
                pattern = 'attention_module'
            elif 'cbam' in key:
                pattern = 'cbam_module'
            elif 'extra_conv' in key:
                pattern = 'extra_conv'
            else:
                pattern = 'other'
                
            patterns[pattern] = patterns.get(pattern, 0) + 1
            
        return patterns
    
    def _detect_compatibility_issues(self, keys: list) -> list:
        """检测兼容性问题"""
        issues = []
        
        # 检查是否有不匹配的键
        has_model_prefix = any(key.startswith('model.') for key in keys)
        has_attention_high = any('attention_high' in key for key in keys)
        has_attention_mid = any('attention_mid' in key for key in keys)
        has_cbam = any('cbam' in key for key in keys)
        has_extra_conv = any('extra_conv' in key for key in keys)
        
        if has_model_prefix:
            issues.append("模型使用了'model.'前缀")
        if has_attention_high or has_attention_mid:
            issues.append("模型包含attention_high/attention_mid模块")
        if has_cbam:
            issues.append("模型包含CBAM注意力模块")
        if has_extra_conv:
            issues.append("模型包含extra_conv模块")
            
        return issues
    
    def create_compatible_model(self, model_path: str, num_classes: int = 29) -> Optional[nn.Module]:
        """创建兼容的模型"""
        try:
            # 分析模型结构
            analysis = self.analyze_model_structure(model_path)
            
            if 'error' in analysis:
                logger.error(f"无法分析模型: {analysis['error']}")
                return None
            
            logger.info(f"模型分析完成:")
            logger.info(f"  - 检查点键: {analysis['checkpoint_keys']}")
            logger.info(f"  - 状态字典键数量: {analysis['state_dict_info']['total_keys']}")
            logger.info(f"  - 兼容性问题: {analysis['compatibility_issues']}")
            
            # 根据分析结果选择加载策略
            if self._is_smart_optimized_model(analysis):
                return self._load_smart_optimized_model(model_path, num_classes)
            else:
                return self._load_generic_model(model_path, num_classes)
                
        except Exception as e:
            logger.error(f"创建兼容模型失败: {e}")
            return None
    
    def _is_smart_optimized_model(self, analysis: Dict) -> bool:
        """判断是否是智能优化模型"""
        issues = analysis.get('compatibility_issues', [])
        return any('CBAM' in issue or 'attention' in issue or 'extra_conv' in issue for issue in issues)
    
    def _load_smart_optimized_model(self, model_path: str, num_classes: int) -> Optional[nn.Module]:
        """加载智能优化模型"""
        try:
            # 导入智能优化模型架构
            from train_smart_optimized import SmartOptimizedUNet
            
            # 创建模型实例
            model = SmartOptimizedUNet(num_classes=num_classes)
            
            # 加载权重
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # 尝试加载权重
            try:
                model.load_state_dict(state_dict, strict=True)
                logger.info("成功加载智能优化模型 (strict=True)")
            except RuntimeError as e:
                logger.warning(f"严格加载失败: {e}")
                # 尝试非严格加载
                missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
                logger.info(f"非严格加载完成:")
                logger.info(f"  - 缺失键数量: {len(missing_keys)}")
                logger.info(f"  - 意外键数量: {len(unexpected_keys)}")
                
                if len(missing_keys) > 10:
                    logger.warning("缺失键过多，可能影响性能")
            
            model.to(self.device)
            model.eval()
            
            logger.info("智能优化模型加载成功")
            return model
            
        except ImportError:
            logger.warning("无法导入智能优化模型，尝试通用加载")
            return self._load_generic_model(model_path, num_classes)
        except Exception as e:
            logger.error(f"加载智能优化模型失败: {e}")
            return None
    
    def _load_generic_model(self, model_path: str, num_classes: int) -> Optional[nn.Module]:
        """加载通用模型"""
        try:
            # 尝试导入标准UNet
            from unet import Unet
            
            # 创建模型实例
            model = Unet(num_classes=num_classes, backbone="resnet50")
            
            # 加载权重
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # 过滤不兼容的键
            filtered_state_dict = self._filter_incompatible_keys(state_dict, model.state_dict())
            
            # 加载过滤后的权重
            missing_keys, unexpected_keys = model.load_state_dict(filtered_state_dict, strict=False)
            
            logger.info(f"通用模型加载完成:")
            logger.info(f"  - 加载键数量: {len(filtered_state_dict)}")
            logger.info(f"  - 缺失键数量: {len(missing_keys)}")
            logger.info(f"  - 意外键数量: {len(unexpected_keys)}")
            
            model.to(self.device)
            model.eval()
            
            logger.info("通用模型加载成功")
            return model
            
        except Exception as e:
            logger.error(f"加载通用模型失败: {e}")
            return None
    
    def _filter_incompatible_keys(self, checkpoint_state_dict: Dict, model_state_dict: Dict) -> Dict:
        """过滤不兼容的键"""
        filtered_dict = {}
        model_keys = set(model_state_dict.keys())
        
        for key, value in checkpoint_state_dict.items():
            if key in model_keys:
                # 检查形状是否匹配
                if value.shape == model_state_dict[key].shape:
                    filtered_dict[key] = value
                else:
                    logger.warning(f"形状不匹配，跳过键: {key}")
            else:
                logger.debug(f"模型中不存在键: {key}")
        
        logger.info(f"过滤后保留 {len(filtered_dict)}/{len(checkpoint_state_dict)} 个键")
        return filtered_dict
    
    def test_model_loading(self, model_path: str) -> bool:
        """测试模型加载"""
        logger.info(f"🧪 测试模型加载: {model_path}")
        
        # 分析模型
        analysis = self.analyze_model_structure(model_path)
        
        if 'error' in analysis:
            logger.error(f"❌ 模型分析失败")
            return False
        
        # 创建兼容模型
        model = self.create_compatible_model(model_path)
        
        if model is None:
            logger.error(f"❌ 模型加载失败")
            return False
        
        # 测试前向传播
        try:
            with torch.no_grad():
                test_input = torch.randn(1, 3, 512, 512).to(self.device)
                output = model(test_input)
                
                logger.info(f"✅ 模型测试成功:")
                logger.info(f"   输入形状: {test_input.shape}")
                logger.info(f"   输出形状: {output.shape}")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 前向传播测试失败: {e}")
            return False
    
    def save_analysis_report(self, model_path: str, output_path: str = None):
        """保存分析报告"""
        if output_path is None:
            output_path = f"model_analysis_{os.path.basename(model_path)}.json"
        
        analysis = self.analyze_model_structure(model_path)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"分析报告已保存: {output_path}")

def main():
    """主函数"""
    print("🔧 模型兼容性修复器")
    print("=" * 60)
    
    model_path = "best_smart_optimized_miou_0.4477.pth"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    # 创建修复器
    fixer = ModelCompatibilityFixer()
    
    # 分析模型
    print("📊 分析模型结构...")
    analysis = fixer.analyze_model_structure(model_path)
    
    if 'error' in analysis:
        print(f"❌ 分析失败: {analysis['error']}")
        return
    
    print("✅ 模型分析完成")
    print(f"   检查点键: {len(analysis['checkpoint_keys'])} 个")
    print(f"   状态字典键: {analysis['state_dict_info']['total_keys']} 个")
    print(f"   兼容性问题: {len(analysis['compatibility_issues'])} 个")
    
    if analysis['compatibility_issues']:
        print("⚠️  发现的问题:")
        for issue in analysis['compatibility_issues']:
            print(f"     - {issue}")
    
    # 测试加载
    print("\n🧪 测试模型加载...")
    success = fixer.test_model_loading(model_path)
    
    if success:
        print("✅ 模型兼容性修复成功！")
        print("📁 可以继续进行真实验证")
    else:
        print("❌ 模型兼容性修复失败")
        print("💡 建议检查模型架构或使用模拟模式")
    
    # 保存分析报告
    fixer.save_analysis_report(model_path)
    print(f"📄 分析报告已保存")

if __name__ == "__main__":
    main()
