#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图像匹配重命名工具
通过图像内容匹配，将D:\123pictures中的图片重命名为与D:\1a_taohuacun中对应的名称
"""

import os
import cv2
import numpy as np
import hashlib
from pathlib import Path
import shutil
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImageMatcher:
    """图像匹配器"""

    def __init__(self, source_dir: str = r"D:\1a_taohuacun", target_dir: str = r"D:\123pictures\images"):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.backup_dir = Path(target_dir + "_backup")

        # 支持的图像格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

        # 匹配结果
        self.matches = {}
        self.unmatched_source = []
        self.unmatched_target = []

    def calculate_image_hash(self, image_path: Path) -> str:
        """计算图像的感知哈希"""
        try:
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                logger.warning(f"无法读取图像: {image_path}")
                return None

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 调整大小为8x8
            resized = cv2.resize(gray, (8, 8))

            # 计算平均值
            avg = resized.mean()

            # 生成哈希
            hash_str = ""
            for i in range(8):
                for j in range(8):
                    hash_str += "1" if resized[i, j] > avg else "0"

            return hash_str

        except Exception as e:
            logger.error(f"计算图像哈希失败 {image_path}: {e}")
            return None

    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件的MD5哈希"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None

    def get_image_features(self, image_path: Path) -> Dict:
        """获取图像特征"""
        try:
            image = cv2.imread(str(image_path))
            if image is None:
                return None

            features = {
                'file_size': image_path.stat().st_size,
                'image_shape': image.shape,
                'file_hash': self.calculate_file_hash(image_path),
                'image_hash': self.calculate_image_hash(image_path),
                'mean_color': np.mean(image, axis=(0, 1)).tolist()
            }

            return features

        except Exception as e:
            logger.error(f"获取图像特征失败 {image_path}: {e}")
            return None

    def scan_directories(self) -> Tuple[Dict, Dict]:
        """扫描两个目录中的图像文件"""
        logger.info("🔍 扫描目录中的图像文件...")

        source_images = {}
        target_images = {}

        # 扫描源目录 (D:\1a_taohuacun)
        if self.source_dir.exists():
            for file_path in self.source_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.image_extensions:
                    features = self.get_image_features(file_path)
                    if features:
                        source_images[file_path.name] = {
                            'path': file_path,
                            'features': features
                        }
            logger.info(f"✅ 源目录找到 {len(source_images)} 张图像")
        else:
            logger.error(f"❌ 源目录不存在: {self.source_dir}")
            return {}, {}

        # 扫描目标目录 (D:\123pictures)
        if self.target_dir.exists():
            for file_path in self.target_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.image_extensions:
                    features = self.get_image_features(file_path)
                    if features:
                        target_images[file_path.name] = {
                            'path': file_path,
                            'features': features
                        }
            logger.info(f"✅ 目标目录找到 {len(target_images)} 张图像")
        else:
            logger.error(f"❌ 目标目录不存在: {self.target_dir}")
            return source_images, {}

        return source_images, target_images

    def calculate_similarity(self, features1: Dict, features2: Dict) -> float:
        """计算两个图像特征的相似度"""
        similarity_score = 0
        total_weight = 0

        # 1. 文件大小相似度 (权重: 0.2)
        if features1['file_size'] > 0 and features2['file_size'] > 0:
            size_ratio = min(features1['file_size'], features2['file_size']) / max(features1['file_size'], features2['file_size'])
            similarity_score += size_ratio * 0.2
            total_weight += 0.2

        # 2. 图像尺寸相似度 (权重: 0.3)
        if features1['image_shape'] == features2['image_shape']:
            similarity_score += 1.0 * 0.3
            total_weight += 0.3

        # 3. 文件哈希匹配 (权重: 0.4)
        if features1['file_hash'] and features2['file_hash']:
            if features1['file_hash'] == features2['file_hash']:
                similarity_score += 1.0 * 0.4
            total_weight += 0.4

        # 4. 图像哈希相似度 (权重: 0.1)
        if features1['image_hash'] and features2['image_hash']:
            hash1 = features1['image_hash']
            hash2 = features2['image_hash']
            if len(hash1) == len(hash2):
                hamming_distance = sum(c1 != c2 for c1, c2 in zip(hash1, hash2))
                hash_similarity = 1 - (hamming_distance / len(hash1))
                similarity_score += hash_similarity * 0.1
                total_weight += 0.1

        return similarity_score / total_weight if total_weight > 0 else 0

    def find_matches(self, source_images: Dict, target_images: Dict) -> None:
        """寻找匹配的图像对"""
        logger.info("🔍 开始匹配图像...")

        matched_targets = set()

        for source_name, source_data in source_images.items():
            best_match = None
            best_similarity = 0

            for target_name, target_data in target_images.items():
                if target_name in matched_targets:
                    continue

                similarity = self.calculate_similarity(
                    source_data['features'],
                    target_data['features']
                )

                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match = target_name

            # 设置相似度阈值
            if best_match and best_similarity > 0.8:  # 80%相似度阈值
                self.matches[source_name] = {
                    'target_name': best_match,
                    'similarity': best_similarity,
                    'source_path': source_data['path'],
                    'target_path': target_images[best_match]['path']
                }
                matched_targets.add(best_match)
                logger.info(f"✅ 匹配: {best_match} → {source_name} (相似度: {best_similarity:.3f})")
            else:
                self.unmatched_source.append(source_name)
                if best_match:
                    logger.warning(f"⚠️ 低相似度: {best_match} → {source_name} (相似度: {best_similarity:.3f})")

        # 找出未匹配的目标图像
        for target_name in target_images.keys():
            if target_name not in matched_targets:
                self.unmatched_target.append(target_name)

        logger.info(f"📊 匹配结果: {len(self.matches)} 对匹配, {len(self.unmatched_source)} 个源文件未匹配, {len(self.unmatched_target)} 个目标文件未匹配")

    def create_backup(self) -> bool:
        """创建目标目录的备份"""
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)

            shutil.copytree(self.target_dir, self.backup_dir)
            logger.info(f"✅ 备份创建成功: {self.backup_dir}")
            return True

        except Exception as e:
            logger.error(f"❌ 备份创建失败: {e}")
            return False

    def perform_rename(self, dry_run: bool = True) -> bool:
        """执行重命名操作"""
        if not self.matches:
            logger.error("❌ 没有找到匹配的图像对")
            return False

        if not dry_run:
            # 创建备份
            if not self.create_backup():
                logger.error("❌ 备份失败，取消重命名操作")
                return False

        success_count = 0
        error_count = 0

        logger.info(f"🚀 开始重命名操作 (干运行: {dry_run})...")

        for source_name, match_data in self.matches.items():
            try:
                old_path = match_data['target_path']
                new_path = self.target_dir / source_name

                if dry_run:
                    logger.info(f"[DRY RUN] {old_path.name} → {source_name}")
                else:
                    # 如果目标文件已存在，添加后缀
                    if new_path.exists():
                        base_name = new_path.stem
                        extension = new_path.suffix
                        counter = 1
                        while new_path.exists():
                            new_path = self.target_dir / f"{base_name}_{counter}{extension}"
                            counter += 1

                    old_path.rename(new_path)
                    logger.info(f"✅ 重命名: {old_path.name} → {new_path.name}")

                success_count += 1

            except Exception as e:
                logger.error(f"❌ 重命名失败 {old_path.name}: {e}")
                error_count += 1

        logger.info(f"📊 重命名完成: {success_count} 成功, {error_count} 失败")

        if not dry_run and error_count == 0:
            logger.info("🎉 所有文件重命名成功！")

        return error_count == 0

    def generate_report(self) -> str:
        """生成匹配报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"image_matching_report_{timestamp}.json"

        report_data = {
            'timestamp': timestamp,
            'source_dir': str(self.source_dir),
            'target_dir': str(self.target_dir),
            'total_matches': len(self.matches),
            'unmatched_source': len(self.unmatched_source),
            'unmatched_target': len(self.unmatched_target),
            'matches': {
                source: {
                    'target_name': data['target_name'],
                    'similarity': data['similarity']
                }
                for source, data in self.matches.items()
            },
            'unmatched_source_files': self.unmatched_source,
            'unmatched_target_files': self.unmatched_target
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        logger.info(f"📄 报告已保存: {report_path}")
        return report_path

    def run_matching_process(self, dry_run: bool = True) -> bool:
        """运行完整的匹配流程"""
        logger.info("🚀 开始图像匹配重命名流程")

        # 1. 扫描目录
        source_images, target_images = self.scan_directories()

        if not source_images or not target_images:
            logger.error("❌ 目录扫描失败")
            return False

        # 2. 寻找匹配
        self.find_matches(source_images, target_images)

        # 3. 生成报告
        report_path = self.generate_report()

        # 4. 执行重命名
        if self.matches:
            success = self.perform_rename(dry_run=dry_run)
            return success
        else:
            logger.error("❌ 没有找到匹配的图像对")
            return False

def main():
    """主函数"""
    print("🖼️ 图像匹配重命名工具")
    print("=" * 60)

    # 检查目录是否存在
    source_dir = r"D:\1a_taohuacun"
    target_dir = r"D:\123pictures\images"

    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        return

    if not os.path.exists(target_dir):
        print(f"❌ 目标目录不存在: {target_dir}")
        return

    print(f"📁 源目录: {source_dir}")
    print(f"📁 目标目录: {target_dir}")

    # 创建匹配器
    matcher = ImageMatcher(source_dir, target_dir)

    # 首先进行干运行
    print("\n🧪 执行干运行 (预览模式)...")
    success = matcher.run_matching_process(dry_run=True)

    if success:
        print(f"\n📊 匹配统计:")
        print(f"   成功匹配: {len(matcher.matches)} 对")
        print(f"   未匹配源文件: {len(matcher.unmatched_source)} 个")
        print(f"   未匹配目标文件: {len(matcher.unmatched_target)} 个")

        if len(matcher.matches) > 0:
            # 询问是否执行实际重命名
            response = input(f"\n❓ 是否执行实际重命名操作？(y/N): ").strip().lower()

            if response == 'y':
                print("\n🚀 执行实际重命名...")
                final_success = matcher.run_matching_process(dry_run=False)

                if final_success:
                    print("🎉 重命名操作完成！")
                    print(f"💾 备份位置: {target_dir}_backup")
                else:
                    print("❌ 重命名操作失败")
            else:
                print("✋ 用户取消操作")
        else:
            print("❌ 没有找到匹配的图像对，无法执行重命名")
    else:
        print("❌ 匹配流程失败")

if __name__ == "__main__":
    main()
