#!/usr/bin/env python
"""
ISAT启动器
"""
import subprocess
import sys
import os

def launch_isat():
    """启动ISAT"""
    print("🚀 正在启动ISAT...")
    
    # ISAT路径
    isat_path = r"C:\Users\<USER>\.conda\envs\isat_env\Lib\site-packages\ISAT\scripts\isat.py"
    python_path = r"C:\Users\<USER>\.conda\envs\isat_env\python.exe"
    
    # 检查文件是否存在
    if not os.path.exists(isat_path):
        print(f"❌ ISAT文件不存在: {isat_path}")
        return False
        
    if not os.path.exists(python_path):
        print(f"❌ Python解释器不存在: {python_path}")
        return False
    
    try:
        # 启动ISAT
        print(f"📂 使用Python: {python_path}")
        print(f"📄 启动脚本: {isat_path}")
        
        # 使用subprocess启动
        process = subprocess.Popen([python_path, isat_path], 
                                 cwd=r"D:\U-net",
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        print(f"✅ ISAT已启动! 进程ID: {process.pid}")
        print("🎯 请检查是否有新的ISAT窗口打开")
        return True
        
    except Exception as e:
        print(f"❌ 启动ISAT失败: {e}")
        return False

if __name__ == "__main__":
    launch_isat()
    input("按Enter键退出...")
