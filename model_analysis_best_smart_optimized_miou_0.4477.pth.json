{"checkpoint_keys": ["epoch", "stage", "model_state_dict", "optimizer_state_dict", "best_miou", "train_loss", "val_loss", "loss_components", "weight_multiplier"], "model_info": {"epoch": 81, "best_miou": 0.4477378726005554, "stage": "Stage2_Normal"}, "state_dict_info": {"total_keys": 717, "sample_keys": ["model.encoder.conv1.weight", "model.encoder.bn1.weight", "model.encoder.bn1.bias", "model.encoder.bn1.running_mean", "model.encoder.bn1.running_var", "model.encoder.bn1.num_batches_tracked", "model.encoder.layer1.0.conv1.weight", "model.encoder.layer1.0.bn1.weight", "model.encoder.layer1.0.bn1.bias", "model.encoder.layer1.0.bn1.running_mean", "model.encoder.layer1.0.bn1.running_var", "model.encoder.layer1.0.bn1.num_batches_tracked", "model.encoder.layer1.0.conv2.weight", "model.encoder.layer1.0.bn2.weight", "model.encoder.layer1.0.bn2.bias", "model.encoder.layer1.0.bn2.running_mean", "model.encoder.layer1.0.bn2.running_var", "model.encoder.layer1.0.bn2.num_batches_tracked", "model.encoder.layer1.0.conv3.weight", "model.encoder.layer1.0.bn3.weight"], "key_patterns": {"model_prefix": 399, "encoder_prefix": 318}}, "compatibility_issues": ["模型使用了'model.'前缀", "模型包含attention_high/attention_mid模块", "模型包含extra_conv模块"], "has_model_state_dict": true}