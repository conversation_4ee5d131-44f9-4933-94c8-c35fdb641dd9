#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UNet++训练监控脚本
"""

import os
import time
import json
import matplotlib.pyplot as plt
from datetime import datetime

def monitor_training():
    """监控训练进度"""
    save_dir = 'unet_plus_plus_training_20250527_143521'
    log_file = os.path.join(save_dir, 'logs', 'training.log')
    
    print("📊 UNet++训练监控")
    print("=" * 50)
    print(f"监控目录: {save_dir}")
    print(f"日志文件: {log_file}")
    print("按Ctrl+C停止监控")
    print("-" * 50)
    
    last_size = 0
    
    while True:
        try:
            if os.path.exists(log_file):
                current_size = os.path.getsize(log_file)
                
                if current_size > last_size:
                    # 读取新内容
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_size)
                        new_content = f.read()
                        if new_content.strip():
                            print(new_content.strip())
                    
                    last_size = current_size
            
            time.sleep(5)  # 每5秒检查一次
            
        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控错误: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_training()
