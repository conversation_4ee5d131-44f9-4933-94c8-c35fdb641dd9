# 后处理配置文件
# 用于优化语义分割模型的后处理效果

# 基础配置
basic:
  num_classes: 29
  device: 'cuda'

# 后处理开关配置
processing_switches:
  use_tta: false                    # 测试时增强 (较慢但效果好)
  use_crf: false                    # 条件随机场 (需要pydensecrf)
  use_morphology: true              # 形态学操作 (快速有效)
  use_watershed: false              # 分水岭算法 (需要skimage)
  use_connected_components: true    # 连通组件分析 (有效)
  use_confidence_filtering: true    # 置信度过滤 (有效)
  use_boundary_refinement: true     # 边界细化 (中等效果)
  use_multi_scale_fusion: false    # 多尺度融合 (较慢)
  use_superpixel_voting: false     # 超像素投票 (需要原图)
  use_edge_aware_smoothing: false  # 边缘感知平滑 (需要原图)
  use_class_specific_processing: true  # 类别特定处理 (精细优化)

# TTA配置
tta_config:
  scales: [0.75, 1.0, 1.25]
  flips: ['none', 'horizontal']
  rotations: [0, 180]
  use_all_combinations: false

# CRF配置
crf_config:
  iterations: 10
  pos_w: 3
  pos_xy_std: 3
  bi_w: 4
  bi_xy_std: 49
  bi_rgb_std: 5

# 形态学操作参数
morph_params:
  opening_kernel: 3
  closing_kernel: 5
  erosion_kernel: 2
  dilation_kernel: 3

# 连通组件参数
cc_params:
  min_size: 50
  max_size: 50000

# 置信度过滤参数
conf_params:
  min_confidence: 0.3
  adaptive_threshold: true
  percentile: 0.3

# 预设配置
presets:
  # 快速配置 - 优先速度
  fast:
    use_tta: false
    use_crf: false
    use_morphology: true
    use_watershed: false
    use_connected_components: false
    use_confidence_filtering: true
    use_boundary_refinement: false
    use_multi_scale_fusion: false
    use_superpixel_voting: false
    use_edge_aware_smoothing: false
    use_class_specific_processing: true
    
  # 平衡配置 - 速度和效果平衡
  balanced:
    use_tta: false
    use_crf: false
    use_morphology: true
    use_watershed: false
    use_connected_components: true
    use_confidence_filtering: true
    use_boundary_refinement: true
    use_multi_scale_fusion: false
    use_superpixel_voting: false
    use_edge_aware_smoothing: false
    use_class_specific_processing: true
    
  # 高质量配置 - 优先效果
  high_quality:
    use_tta: true
    use_crf: true
    use_morphology: true
    use_watershed: true
    use_connected_components: true
    use_confidence_filtering: true
    use_boundary_refinement: true
    use_multi_scale_fusion: true
    use_superpixel_voting: true
    use_edge_aware_smoothing: true
    use_class_specific_processing: true

# 类别特定配置
class_specific:
  # 小物体类别
  small_objects: [1, 2, 3, 5, 6, 9, 16, 17]  # 飞机、自行车、鸟、瓶子等
  # 大物体类别  
  large_objects: [7, 8, 11, 19, 22, 23, 24, 25, 26, 27, 28]  # 公交车、汽车、建筑等
  # 背景类别
  background_objects: [21, 22, 23, 24, 26, 27, 28]  # 墙、建筑、天空、地板、树、天花板、道路
  
  # 类别特定的形态学参数
  morph_params_by_class:
    small_objects:
      open_kernel_size: 2
      close_kernel_size: 3
    large_objects:
      open_kernel_size: 4
      close_kernel_size: 6
    default:
      open_kernel_size: 3
      close_kernel_size: 5
      
  # 类别特定的连通组件阈值
  cc_params_by_class:
    small_objects:
      min_size: 20
      max_size: 10000
    large_objects:
      min_size: 100
      max_size: 100000
    background_objects:
      min_size: 500
      max_size: 200000
    default:
      min_size: 50
      max_size: 50000

# 性能优化配置
performance:
  # 并行处理
  use_multiprocessing: false
  num_workers: 4
  
  # 内存优化
  process_in_chunks: false
  chunk_size: 512
  
  # GPU优化
  use_gpu_acceleration: true
  mixed_precision: false

# 调试配置
debug:
  save_intermediate_results: false
  output_dir: 'debug_postprocessing'
  verbose_logging: false
  timing_analysis: true

# 评估配置
evaluation:
  # 评估指标
  metrics: ['miou', 'pixel_accuracy', 'class_accuracy']
  
  # 可视化
  save_visualizations: false
  visualization_classes: [0, 15, 22, 23, 28]  # 背景、人、建筑、天空、道路
  
  # 对比分析
  compare_with_baseline: true
  baseline_config: 'fast'
