
# 🔍 后处理效果评估报告

## 📊 总体结果

**基线性能 (无后处理)**:
- 平均mIoU: 0.0499
- 处理时间: 0.000s

**后处理配置对比**:


### FAST 配置
- 平均mIoU: 0.0982
- mIoU提升: +0.0483 (+96.8%)
- 平均处理时间: 0.172s
- 标准差: ±0.0057


### BALANCED 配置
- 平均mIoU: 0.1005
- mIoU提升: +0.0506 (+101.3%)
- 平均处理时间: 0.190s
- 标准差: ±0.0000


### HIGH_QUALITY 配置
- 平均mIoU: 0.1005
- mIoU提升: +0.0506 (+101.3%)
- 平均处理时间: 0.483s
- 标准差: ±0.0000


## 🏆 推荐配置

**最佳配置**: BALANCED
- 最高mIoU: 0.1005
- 相比基线提升: +0.0506

## 💡 建议

建议使用 balanced 配置进行后处理，可以显著提升模型性能。

## ⚡ 性能分析

- **Fast配置**: 速度最快，适合实时应用
- **Balanced配置**: 平衡速度和效果，推荐日常使用  
- **High Quality配置**: 效果最好，适合离线处理

---
*报告生成时间: 2025-05-27 14:15:00*
