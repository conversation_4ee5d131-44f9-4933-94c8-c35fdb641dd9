#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UNet++自动训练脚本
生成时间: 2025-05-27 14:35:21
"""

import os
import sys
import json
import torch
import logging
from datetime import datetime

# 添加路径
sys.path.append('.')

# 导入训练模块
from train_unet_plus_plus import UNetPlusPlusTrainer
from enhanced_postprocessing_fixed import create_postprocessor

def main():
    """主训练函数"""
    print("🚀 UNet++训练开始")
    print("=" * 60)
    
    # 加载配置
    config_path = 'unet_plus_plus_training_config_20250527_143521.json'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"📊 训练配置:")
    print(f"   模型: {config['model_name']}")
    print(f"   类别数: {config['num_classes']}")
    print(f"   批次大小: {config['batch_size']}")
    print(f"   训练轮数: {config['epochs']}")
    print(f"   学习率: {config['learning_rate']}")
    print(f"   设备: {torch.device('cuda' if torch.cuda.is_available() else 'cpu')}")
    
    # 创建训练器
    trainer = UNetPlusPlusTrainer(config)
    
    # 开始训练
    print("\n🎯 开始UNet++训练...")
    try:
        best_miou = trainer.train()
        
        print("\n🎉 训练完成！")
        print(f"📊 最佳mIoU: {best_miou:.4f}")
        
        # 与当前最佳模型对比
        current_best = 0.4477
        if best_miou > current_best:
            improvement = best_miou - current_best
            print(f"🏆 新记录！提升: +{improvement:.4f}")
            
            if best_miou >= 0.5:
                print("🎊 恭喜！达到论文级别性能 (≥0.5)！")
            else:
                remaining = 0.5 - best_miou
                print(f"📈 距离论文级别: {remaining:.4f}")
        else:
            print(f"📊 当前最佳仍为: {current_best:.4f}")
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
