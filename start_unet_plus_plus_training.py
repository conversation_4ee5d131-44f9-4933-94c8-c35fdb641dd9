#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UNet++训练启动器
开始Phase 2架构升级
"""

import os
import sys
import logging
import json
import torch
from datetime import datetime
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UNetPlusPlusTrainingLauncher:
    """UNet++训练启动器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def check_prerequisites(self) -> bool:
        """检查训练前提条件"""
        logger.info("🔍 检查UNet++训练前提条件...")
        
        # 检查必要文件
        required_files = [
            'unet_plus_plus.py',
            'train_unet_plus_plus.py', 
            'config_unet_plus_plus.yaml',
            'enhanced_postprocessing_fixed.py',
            'postprocessing_config.yaml'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            logger.error(f"❌ 缺少必要文件: {missing_files}")
            return False
        
        # 检查GPU
        if not torch.cuda.is_available():
            logger.warning("⚠️ 未检测到GPU，将使用CPU训练（速度较慢）")
        else:
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"✅ GPU检测成功: {gpu_count}x {gpu_name} ({gpu_memory:.1f}GB)")
        
        # 检查数据集
        data_paths = [
            'UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/JPEGImages',
            'UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/SegmentationClass'
        ]
        
        for path in data_paths:
            if not os.path.exists(path):
                logger.warning(f"⚠️ 数据路径不存在: {path}")
            else:
                file_count = len([f for f in os.listdir(path) if f.endswith(('.jpg', '.png'))])
                logger.info(f"✅ 数据检查: {path} ({file_count} 文件)")
        
        logger.info("✅ 前提条件检查完成")
        return True
    
    def create_training_config(self) -> Dict[str, Any]:
        """创建训练配置"""
        logger.info("📝 创建UNet++训练配置...")
        
        config = {
            # 基础配置
            'model_name': 'unet_plus_plus',
            'num_classes': 29,
            'input_shape': [512, 512],
            'backbone': 'resnet50',
            
            # 训练配置
            'batch_size': 4,  # 适中的批次大小
            'epochs': 100,    # 充足的训练轮数
            'learning_rate': 0.001,
            'weight_decay': 1e-4,
            'momentum': 0.9,
            
            # 优化策略
            'optimizer': 'adamw',
            'scheduler': 'cosine',
            'warmup_epochs': 5,
            'mixed_precision': True,
            
            # 损失函数配置
            'loss_config': {
                'focal_weight': 0.4,
                'dice_weight': 0.3,
                'ce_weight': 0.3,
                'deep_supervision': True,
                'deep_supervision_weights': [0.5, 0.3, 0.15, 0.05]  # 4个输出的权重
            },
            
            # 数据增强
            'augmentation': {
                'horizontal_flip': 0.5,
                'vertical_flip': 0.2,
                'rotation': 15,
                'brightness': 0.2,
                'contrast': 0.2,
                'saturation': 0.2,
                'hue': 0.1,
                'mixup_alpha': 0.2,
                'cutmix_alpha': 1.0
            },
            
            # 类别权重（基于之前的分析）
            'class_weights': {
                'strategy': 'smart_balanced',
                'base_weights': [1.0] * 29,  # 将根据数据分布自动调整
                'rare_class_boost': 5.0,
                'common_class_penalty': 0.5
            },
            
            # 验证和保存
            'validation_frequency': 5,
            'save_frequency': 10,
            'early_stopping_patience': 20,
            'save_best_only': True,
            
            # 后处理集成
            'postprocessing': {
                'enabled': True,
                'config': 'balanced',  # 使用balanced配置
                'apply_during_validation': True
            },
            
            # 日志和监控
            'logging': {
                'log_frequency': 10,
                'tensorboard': True,
                'wandb': False,  # 可选
                'save_predictions': True,
                'save_frequency': 20
            },
            
            # 路径配置
            'paths': {
                'data_root': 'UNet_Demo/UNet_Demo/VOCdevkit/VOC2007',
                'save_dir': f'unet_plus_plus_training_{self.timestamp}',
                'pretrained_weights': None,  # 从头开始训练
                'resume_from': None
            }
        }
        
        # 保存配置
        config_path = f'unet_plus_plus_training_config_{self.timestamp}.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 训练配置已保存: {config_path}")
        return config
    
    def prepare_training_environment(self, config: Dict[str, Any]) -> bool:
        """准备训练环境"""
        logger.info("🛠️ 准备UNet++训练环境...")
        
        # 创建保存目录
        save_dir = config['paths']['save_dir']
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(os.path.join(save_dir, 'checkpoints'), exist_ok=True)
        os.makedirs(os.path.join(save_dir, 'logs'), exist_ok=True)
        os.makedirs(os.path.join(save_dir, 'predictions'), exist_ok=True)
        
        logger.info(f"✅ 训练目录已创建: {save_dir}")
        
        # 复制配置文件到训练目录
        import shutil
        config_files = [
            'config_unet_plus_plus.yaml',
            'postprocessing_config.yaml'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, save_dir)
                logger.info(f"✅ 配置文件已复制: {config_file}")
        
        return True
    
    def create_training_script(self, config: Dict[str, Any]) -> str:
        """创建训练脚本"""
        logger.info("📝 创建UNet++训练脚本...")
        
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UNet++自动训练脚本
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import os
import sys
import json
import torch
import logging
from datetime import datetime

# 添加路径
sys.path.append('.')

# 导入训练模块
from train_unet_plus_plus import UNetPlusPlusTrainer
from enhanced_postprocessing_fixed import create_postprocessor

def main():
    """主训练函数"""
    print("🚀 UNet++训练开始")
    print("=" * 60)
    
    # 加载配置
    config_path = 'unet_plus_plus_training_config_{self.timestamp}.json'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"📊 训练配置:")
    print(f"   模型: {{config['model_name']}}")
    print(f"   类别数: {{config['num_classes']}}")
    print(f"   批次大小: {{config['batch_size']}}")
    print(f"   训练轮数: {{config['epochs']}}")
    print(f"   学习率: {{config['learning_rate']}}")
    print(f"   设备: {{torch.device('cuda' if torch.cuda.is_available() else 'cpu')}}")
    
    # 创建训练器
    trainer = UNetPlusPlusTrainer(config)
    
    # 开始训练
    print("\\n🎯 开始UNet++训练...")
    try:
        best_miou = trainer.train()
        
        print("\\n🎉 训练完成！")
        print(f"📊 最佳mIoU: {{best_miou:.4f}}")
        
        # 与当前最佳模型对比
        current_best = 0.4477
        if best_miou > current_best:
            improvement = best_miou - current_best
            print(f"🏆 新记录！提升: +{{improvement:.4f}}")
            
            if best_miou >= 0.5:
                print("🎊 恭喜！达到论文级别性能 (≥0.5)！")
            else:
                remaining = 0.5 - best_miou
                print(f"📈 距离论文级别: {{remaining:.4f}}")
        else:
            print(f"📊 当前最佳仍为: {{current_best:.4f}}")
            
    except Exception as e:
        print(f"❌ 训练失败: {{e}}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
        
        script_path = f'run_unet_plus_plus_training_{self.timestamp}.py'
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        logger.info(f"✅ 训练脚本已创建: {script_path}")
        return script_path
    
    def create_monitoring_script(self, config: Dict[str, Any]) -> str:
        """创建监控脚本"""
        logger.info("📊 创建训练监控脚本...")
        
        monitor_script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UNet++训练监控脚本
"""

import os
import time
import json
import matplotlib.pyplot as plt
from datetime import datetime

def monitor_training():
    """监控训练进度"""
    save_dir = '{config['paths']['save_dir']}'
    log_file = os.path.join(save_dir, 'logs', 'training.log')
    
    print("📊 UNet++训练监控")
    print("=" * 50)
    print(f"监控目录: {{save_dir}}")
    print(f"日志文件: {{log_file}}")
    print("按Ctrl+C停止监控")
    print("-" * 50)
    
    last_size = 0
    
    while True:
        try:
            if os.path.exists(log_file):
                current_size = os.path.getsize(log_file)
                
                if current_size > last_size:
                    # 读取新内容
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_size)
                        new_content = f.read()
                        if new_content.strip():
                            print(new_content.strip())
                    
                    last_size = current_size
            
            time.sleep(5)  # 每5秒检查一次
            
        except KeyboardInterrupt:
            print("\\n监控已停止")
            break
        except Exception as e:
            print(f"监控错误: {{e}}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_training()
'''
        
        monitor_path = f'monitor_unet_plus_plus_{self.timestamp}.py'
        with open(monitor_path, 'w', encoding='utf-8') as f:
            f.write(monitor_script)
        
        logger.info(f"✅ 监控脚本已创建: {monitor_path}")
        return monitor_path
    
    def launch_training(self) -> bool:
        """启动UNet++训练"""
        logger.info("🚀 启动UNet++训练流程...")
        
        # 1. 检查前提条件
        if not self.check_prerequisites():
            logger.error("❌ 前提条件检查失败")
            return False
        
        # 2. 创建训练配置
        config = self.create_training_config()
        
        # 3. 准备训练环境
        if not self.prepare_training_environment(config):
            logger.error("❌ 训练环境准备失败")
            return False
        
        # 4. 创建训练脚本
        training_script = self.create_training_script(config)
        
        # 5. 创建监控脚本
        monitor_script = self.create_monitoring_script(config)
        
        # 6. 生成启动报告
        self.generate_launch_report(config, training_script, monitor_script)
        
        logger.info("✅ UNet++训练准备完成！")
        return True
    
    def generate_launch_report(self, config: Dict[str, Any], training_script: str, monitor_script: str):
        """生成启动报告"""
        report = f'''# 🚀 UNet++训练启动报告

## 📊 训练配置概览

**时间戳**: {self.timestamp}
**模型**: UNet++ with ResNet50 backbone
**类别数**: {config['num_classes']}
**训练轮数**: {config['epochs']}
**批次大小**: {config['batch_size']}
**学习率**: {config['learning_rate']}

## 🎯 训练目标

- **当前最佳**: 0.4477 mIoU
- **目标性能**: ≥0.5000 mIoU (论文级别)
- **预期提升**: +0.0523 mIoU (11.7%)

## 🔧 关键特性

### 架构优势
- ✅ **UNet++**: 密集跳跃连接，更好的特征融合
- ✅ **CBAM注意力**: 通道和空间注意力机制
- ✅ **深度监督**: 多尺度输出，更好的梯度流
- ✅ **ResNet50**: 强大的特征提取能力

### 训练策略
- ✅ **混合损失**: Focal + Dice + CrossEntropy
- ✅ **智能权重**: 自适应类别平衡
- ✅ **数据增强**: MixUp + CutMix + 传统增强
- ✅ **混合精度**: 加速训练，节省显存

### 后处理集成
- ✅ **实时后处理**: 验证时自动应用
- ✅ **配置优化**: 使用balanced配置
- ✅ **性能监控**: 实时效果评估

## 📁 文件结构

```
{config['paths']['save_dir']}/
├── checkpoints/          # 模型检查点
├── logs/                 # 训练日志
├── predictions/          # 预测结果
├── config_unet_plus_plus.yaml
└── postprocessing_config.yaml
```

## 🚀 启动命令

### 开始训练
```bash
python {training_script}
```

### 监控训练
```bash
python {monitor_script}
```

## 📊 预期时间线

- **Phase 2a** (0-20 epochs): 基础收敛，mIoU 0.1-0.2
- **Phase 2b** (20-50 epochs): 快速提升，mIoU 0.2-0.35
- **Phase 2c** (50-80 epochs): 精细调优，mIoU 0.35-0.45
- **Phase 2d** (80-100 epochs): 最终冲刺，mIoU 0.45-0.5+

## 🎯 成功指标

### 最低目标
- **mIoU ≥ 0.47**: 超越当前最佳
- **稳定收敛**: 验证损失持续下降
- **无过拟合**: 训练/验证差距 <0.1

### 理想目标  
- **mIoU ≥ 0.50**: 达到论文级别
- **快速收敛**: 50轮内达到0.45
- **鲁棒性强**: 多次运行结果一致

### 突破目标
- **mIoU ≥ 0.55**: 超越论文级别
- **SOTA性能**: 在该数据集上达到最佳
- **工业应用**: 可直接部署使用

## 💡 监控要点

1. **损失曲线**: 确保平滑下降
2. **mIoU趋势**: 关注验证集性能
3. **类别平衡**: 检查各类别IoU
4. **内存使用**: 避免OOM错误
5. **收敛速度**: 评估训练效率

## 🔄 后续计划

### 如果成功 (mIoU ≥ 0.5)
1. **模型集成**: 与现有最佳模型ensemble
2. **超参优化**: 进一步调优
3. **部署准备**: 模型压缩和优化

### 如果需要改进 (mIoU < 0.5)
1. **损失函数**: 尝试Lovász Loss
2. **数据策略**: 增强数据增强
3. **架构调整**: 尝试更强backbone

---
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**下一步**: 执行训练脚本开始Phase 2
'''
        
        report_path = f'unet_plus_plus_launch_report_{self.timestamp}.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 启动报告已生成: {report_path}")

def main():
    """主函数"""
    print("🚀 UNet++训练启动器")
    print("=" * 60)
    
    launcher = UNetPlusPlusTrainingLauncher()
    
    if launcher.launch_training():
        print("✅ UNet++训练准备完成！")
        print("\n📋 下一步操作:")
        print(f"   1. 执行训练: python run_unet_plus_plus_training_{launcher.timestamp}.py")
        print(f"   2. 监控进度: python monitor_unet_plus_plus_{launcher.timestamp}.py")
        print(f"   3. 查看报告: unet_plus_plus_launch_report_{launcher.timestamp}.md")
        print("\n🎯 目标: 突破0.5 mIoU，达到论文级别性能！")
    else:
        print("❌ UNet++训练准备失败")

if __name__ == "__main__":
    main()
