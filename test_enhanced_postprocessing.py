#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import numpy as np
import cv2
import os
import glob
from PIL import Image
import logging
from datetime import datetime
import json

# 导入我们的模块
from enhanced_postprocessing import EnhancedPostProcessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PostProcessingEvaluator:
    """后处理效果评估器"""

    def __init__(self, model_path, num_classes=29, device='cuda'):
        self.device = device
        self.num_classes = num_classes

        # 加载最佳模型
        self.model = self._load_model(model_path)

        # 初始化后处理器
        self.postprocessor = EnhancedPostProcessor(num_classes=num_classes, device=device)

        # 评估结果
        self.results = {
            'without_postprocessing': [],
            'with_postprocessing': [],
            'improvement': [],
            'processing_times': [],
        }

    def _load_model(self, model_path):
        """加载模型"""
        try:
            # 添加路径到sys.path
            import sys
            sys.path.append('UNet_Demo/UNet_Demo')

            from unet import Unet

            model = Unet(num_classes=self.num_classes, backbone="resnet50")

            if os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                else:
                    model.load_state_dict(checkpoint)
                logger.info(f"成功加载模型: {model_path}")
            else:
                logger.warning(f"模型文件不存在: {model_path}")

            model.to(self.device)
            model.eval()
            return model

        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return None

    def evaluate_on_dataset(self, image_dir, label_dir, num_samples=50):
        """在数据集上评估后处理效果"""
        logger.info(f"开始评估后处理效果，样本数: {num_samples}")

        # 获取图像文件列表
        image_files = glob.glob(os.path.join(image_dir, "*.jpg")) + \
                     glob.glob(os.path.join(image_dir, "*.png"))

        if len(image_files) == 0:
            logger.error(f"在 {image_dir} 中未找到图像文件")
            return

        # 限制样本数量
        image_files = image_files[:num_samples]

        total_miou_without = 0
        total_miou_with = 0
        valid_samples = 0

        for i, image_path in enumerate(image_files):
            try:
                # 加载图像和标签
                image, label = self._load_image_and_label(image_path, label_dir)
                if image is None or label is None:
                    continue

                # 模型预测
                with torch.no_grad():
                    logits = self._predict(image)

                # 不使用后处理的结果
                pred_without = torch.argmax(logits, dim=1)[0].cpu().numpy()
                miou_without = self._calculate_miou(pred_without, label)

                # 使用后处理的结果
                pred_with, confidence_map, processing_info = self.postprocessor.process_prediction(
                    logits[0], original_image=image, original_size=label.shape
                )
                miou_with = self._calculate_miou(pred_with, label)

                # 记录结果
                self.results['without_postprocessing'].append(miou_without)
                self.results['with_postprocessing'].append(miou_with)
                self.results['improvement'].append(miou_with - miou_without)
                self.results['processing_times'].append(processing_info.get('total_time', 0))

                total_miou_without += miou_without
                total_miou_with += miou_with
                valid_samples += 1

                if (i + 1) % 10 == 0:
                    logger.info(f"已处理 {i+1}/{len(image_files)} 个样本")

            except Exception as e:
                logger.warning(f"处理样本 {image_path} 失败: {e}")
                continue

        if valid_samples > 0:
            avg_miou_without = total_miou_without / valid_samples
            avg_miou_with = total_miou_with / valid_samples
            improvement = avg_miou_with - avg_miou_without

            logger.info(f"评估完成！")
            logger.info(f"有效样本数: {valid_samples}")
            logger.info(f"无后处理平均mIoU: {avg_miou_without:.4f}")
            logger.info(f"有后处理平均mIoU: {avg_miou_with:.4f}")
            logger.info(f"平均提升: {improvement:.4f} ({improvement/avg_miou_without*100:.2f}%)")

            return {
                'avg_miou_without': avg_miou_without,
                'avg_miou_with': avg_miou_with,
                'improvement': improvement,
                'improvement_percentage': improvement/avg_miou_without*100,
                'valid_samples': valid_samples
            }
        else:
            logger.error("没有有效的样本进行评估")
            return None

    def _load_image_and_label(self, image_path, label_dir):
        """加载图像和对应的标签"""
        try:
            # 加载图像
            image = cv2.imread(image_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 查找对应的标签文件
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            label_path = os.path.join(label_dir, base_name + ".png")

            if not os.path.exists(label_path):
                logger.warning(f"标签文件不存在: {label_path}")
                return None, None

            # 加载标签
            label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)

            return image, label

        except Exception as e:
            logger.warning(f"加载图像和标签失败: {e}")
            return None, None

    def _predict(self, image):
        """模型预测"""
        # 预处理图像
        image_tensor = self._preprocess_image(image)

        # 模型推理
        with torch.no_grad():
            logits = self.model(image_tensor)

        return logits

    def _preprocess_image(self, image):
        """图像预处理"""
        # 调整尺寸
        image_resized = cv2.resize(image, (512, 512))

        # 归一化
        image_normalized = image_resized.astype(np.float32) / 255.0

        # 转换为tensor
        image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
        image_tensor = image_tensor.to(self.device)

        return image_tensor

    def _calculate_miou(self, pred, label):
        """计算mIoU"""
        # 确保尺寸匹配
        if pred.shape != label.shape:
            pred = cv2.resize(pred.astype(np.uint8), (label.shape[1], label.shape[0]),
                            interpolation=cv2.INTER_NEAREST)

        miou = 0
        valid_classes = 0

        for class_id in range(self.num_classes):
            pred_mask = (pred == class_id)
            label_mask = (label == class_id)

            intersection = np.logical_and(pred_mask, label_mask).sum()
            union = np.logical_or(pred_mask, label_mask).sum()

            if union > 0:
                iou = intersection / union
                miou += iou
                valid_classes += 1

        return miou / valid_classes if valid_classes > 0 else 0

    def generate_report(self, output_path="postprocessing_evaluation_report.json"):
        """生成评估报告"""
        if not self.results['without_postprocessing']:
            logger.warning("没有评估结果可以生成报告")
            return

        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_samples': len(self.results['without_postprocessing']),
                'avg_miou_without': np.mean(self.results['without_postprocessing']),
                'avg_miou_with': np.mean(self.results['with_postprocessing']),
                'avg_improvement': np.mean(self.results['improvement']),
                'improvement_std': np.std(self.results['improvement']),
                'avg_processing_time': np.mean(self.results['processing_times']),
                'positive_improvements': sum(1 for x in self.results['improvement'] if x > 0),
                'negative_improvements': sum(1 for x in self.results['improvement'] if x < 0),
            },
            'detailed_results': {
                'without_postprocessing': self.results['without_postprocessing'],
                'with_postprocessing': self.results['with_postprocessing'],
                'improvements': self.results['improvement'],
                'processing_times': self.results['processing_times'],
            }
        }

        # 计算改善百分比
        if report['summary']['avg_miou_without'] > 0:
            improvement_percentage = (report['summary']['avg_improvement'] /
                                    report['summary']['avg_miou_without']) * 100
            report['summary']['improvement_percentage'] = improvement_percentage

        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        logger.info(f"评估报告已保存到: {output_path}")

        # 打印摘要
        print("\n" + "="*60)
        print("🔍 后处理效果评估报告")
        print("="*60)
        print(f"📊 总样本数: {report['summary']['total_samples']}")
        print(f"📈 无后处理平均mIoU: {report['summary']['avg_miou_without']:.4f}")
        print(f"🚀 有后处理平均mIoU: {report['summary']['avg_miou_with']:.4f}")
        print(f"⬆️  平均提升: {report['summary']['avg_improvement']:.4f}")
        if 'improvement_percentage' in report['summary']:
            print(f"📊 提升百分比: {report['summary']['improvement_percentage']:.2f}%")
        print(f"⏱️  平均处理时间: {report['summary']['avg_processing_time']:.3f}s")
        print(f"✅ 正向改善样本: {report['summary']['positive_improvements']}")
        print(f"❌ 负向改善样本: {report['summary']['negative_improvements']}")
        print("="*60)

def main():
    """主函数"""
    # 配置
    model_path = "best_smart_optimized_miou_0.4477.pth"  # 最佳模型路径
    image_dir = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/JPEGImages"  # 图像目录
    label_dir = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007/SegmentationClass"  # 标签目录

    # 创建评估器
    evaluator = PostProcessingEvaluator(model_path)

    if evaluator.model is None:
        logger.error("模型加载失败，无法进行评估")
        return

    # 进行评估
    results = evaluator.evaluate_on_dataset(image_dir, label_dir, num_samples=30)

    if results:
        # 生成报告
        evaluator.generate_report()

        # 如果有显著提升，建议应用后处理
        if results['improvement'] > 0.01:  # 提升超过1%
            print(f"\n🎉 后处理显著提升了模型性能！")
            print(f"建议在实际应用中启用后处理模块")
        else:
            print(f"\n⚠️ 后处理提升有限，可能需要调整参数")

if __name__ == "__main__":
    main()
