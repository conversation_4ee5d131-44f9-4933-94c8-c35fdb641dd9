#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import numpy as np
import cv2
import time
import logging
from enhanced_postprocessing import EnhancedPostProcessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_synthetic_data():
    """创建合成测试数据"""
    # 创建模拟的logits输出 [C, H, W]
    num_classes = 29
    height, width = 256, 256
    
    # 生成随机logits
    logits = torch.randn(num_classes, height, width)
    
    # 创建一些明显的区域来测试后处理效果
    # 背景区域
    logits[0, :50, :] = 5.0  # 强背景信号
    
    # 建筑区域
    logits[22, 50:150, 50:200] = 4.0  # 建筑类别
    
    # 道路区域
    logits[28, 200:, :] = 3.5  # 道路类别
    
    # 天空区域
    logits[23, :80, :] = 3.0  # 天空类别
    
    # 人物区域
    logits[15, 100:180, 100:130] = 2.5  # 人物类别
    
    # 创建对应的原始图像
    image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    # 添加一些结构化的图像内容
    # 天空区域 - 蓝色
    image[:80, :] = [135, 206, 235]
    
    # 建筑区域 - 灰色
    image[50:150, 50:200] = [128, 128, 128]
    
    # 道路区域 - 深灰色
    image[200:, :] = [64, 64, 64]
    
    # 人物区域 - 肤色
    image[100:180, 100:130] = [255, 220, 177]
    
    return logits, image

def calculate_miou(pred1, pred2, num_classes):
    """计算两个预测结果之间的mIoU差异"""
    miou_diff = 0
    valid_classes = 0
    
    for class_id in range(num_classes):
        mask1 = (pred1 == class_id)
        mask2 = (pred2 == class_id)
        
        intersection = np.logical_and(mask1, mask2).sum()
        union = np.logical_or(mask1, mask2).sum()
        
        if union > 0:
            iou = intersection / union
            miou_diff += iou
            valid_classes += 1
    
    return miou_diff / valid_classes if valid_classes > 0 else 0

def test_postprocessing_components():
    """测试各个后处理组件"""
    print("🔬 测试增强后处理模块")
    print("=" * 60)
    
    # 创建后处理器
    postprocessor = EnhancedPostProcessor(num_classes=29, device='cpu')
    
    # 创建测试数据
    logits, image = create_synthetic_data()
    
    print(f"📊 测试数据:")
    print(f"   Logits形状: {logits.shape}")
    print(f"   图像形状: {image.shape}")
    
    # 获取原始预测
    original_pred = torch.argmax(logits, dim=0).numpy()
    
    print(f"\n🎯 原始预测统计:")
    unique, counts = np.unique(original_pred, return_counts=True)
    for class_id, count in zip(unique, counts):
        print(f"   类别 {class_id}: {count} 像素 ({count/original_pred.size*100:.1f}%)")
    
    # 测试各个后处理组件
    test_configs = [
        {
            'name': '仅置信度过滤',
            'config': {
                'use_confidence_filtering': True,
                'use_morphology': False,
                'use_connected_components': False,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_superpixel_voting': False,
                'use_edge_aware_smoothing': False,
                'use_class_specific_processing': False,
                'use_multi_scale_fusion': False,
            }
        },
        {
            'name': '形态学操作',
            'config': {
                'use_confidence_filtering': False,
                'use_morphology': True,
                'use_connected_components': False,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_superpixel_voting': False,
                'use_edge_aware_smoothing': False,
                'use_class_specific_processing': False,
                'use_multi_scale_fusion': False,
            }
        },
        {
            'name': '连通组件分析',
            'config': {
                'use_confidence_filtering': False,
                'use_morphology': False,
                'use_connected_components': True,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_superpixel_voting': False,
                'use_edge_aware_smoothing': False,
                'use_class_specific_processing': False,
                'use_multi_scale_fusion': False,
            }
        },
        {
            'name': '边界细化',
            'config': {
                'use_confidence_filtering': False,
                'use_morphology': False,
                'use_connected_components': False,
                'use_watershed': False,
                'use_boundary_refinement': True,
                'use_superpixel_voting': False,
                'use_edge_aware_smoothing': False,
                'use_class_specific_processing': False,
                'use_multi_scale_fusion': False,
            }
        },
        {
            'name': '类别特定处理',
            'config': {
                'use_confidence_filtering': False,
                'use_morphology': False,
                'use_connected_components': False,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_superpixel_voting': False,
                'use_edge_aware_smoothing': False,
                'use_class_specific_processing': True,
                'use_multi_scale_fusion': False,
            }
        },
        {
            'name': '全部后处理',
            'config': {
                'use_confidence_filtering': True,
                'use_morphology': True,
                'use_connected_components': True,
                'use_watershed': True,
                'use_boundary_refinement': True,
                'use_superpixel_voting': True,
                'use_edge_aware_smoothing': True,
                'use_class_specific_processing': True,
                'use_multi_scale_fusion': True,
            }
        }
    ]
    
    results = []
    
    for test_config in test_configs:
        print(f"\n🧪 测试: {test_config['name']}")
        print("-" * 40)
        
        # 更新配置
        postprocessor.config.update(test_config['config'])
        
        # 执行后处理
        start_time = time.time()
        try:
            processed_pred, confidence_map, processing_info = postprocessor.process_prediction(
                logits.unsqueeze(0),  # 添加batch维度
                original_image=image,
                original_size=None
            )
            
            processing_time = time.time() - start_time
            
            # 计算变化
            changed_pixels = np.sum(processed_pred != original_pred)
            change_percentage = changed_pixels / original_pred.size * 100
            
            # 计算一致性（作为质量指标）
            consistency = calculate_miou(original_pred, processed_pred, 29)
            
            print(f"   ✅ 处理成功")
            print(f"   ⏱️  处理时间: {processing_time:.3f}s")
            print(f"   🔄 变化像素: {changed_pixels} ({change_percentage:.1f}%)")
            print(f"   📊 一致性: {consistency:.3f}")
            
            if 'total_time' in processing_info:
                print(f"   📈 内部计时: {processing_info['total_time']:.3f}s")
            
            results.append({
                'name': test_config['name'],
                'success': True,
                'processing_time': processing_time,
                'changed_pixels': changed_pixels,
                'change_percentage': change_percentage,
                'consistency': consistency
            })
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            results.append({
                'name': test_config['name'],
                'success': False,
                'error': str(e)
            })
    
    # 生成总结报告
    print(f"\n📋 后处理测试总结")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ 成功测试: {len(successful_tests)}/{len(results)}")
    print(f"❌ 失败测试: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        print(f"\n⏱️  处理时间统计:")
        times = [r['processing_time'] for r in successful_tests]
        print(f"   平均: {np.mean(times):.3f}s")
        print(f"   最快: {np.min(times):.3f}s ({successful_tests[np.argmin(times)]['name']})")
        print(f"   最慢: {np.max(times):.3f}s ({successful_tests[np.argmax(times)]['name']})")
        
        print(f"\n🔄 变化程度统计:")
        changes = [r['change_percentage'] for r in successful_tests]
        print(f"   平均变化: {np.mean(changes):.1f}%")
        print(f"   最小变化: {np.min(changes):.1f}% ({successful_tests[np.argmin(changes)]['name']})")
        print(f"   最大变化: {np.max(changes):.1f}% ({successful_tests[np.argmax(changes)]['name']})")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"   {test['name']}: {test['error']}")
    
    print(f"\n🎉 后处理模块测试完成！")
    
    return results

def main():
    """主函数"""
    print("🚀 启动后处理模块测试")
    
    # 检查依赖
    try:
        import cv2
        import numpy as np
        print("✅ 基础依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        return
    
    # 运行测试
    results = test_postprocessing_components()
    
    # 简单的性能建议
    successful_results = [r for r in results if r['success']]
    if successful_results:
        # 找到变化最大但时间合理的配置
        best_config = None
        best_score = 0
        
        for result in successful_results:
            # 综合评分：变化程度 / 处理时间
            if result['processing_time'] > 0:
                score = result['change_percentage'] / result['processing_time']
                if score > best_score:
                    best_score = score
                    best_config = result
        
        if best_config:
            print(f"\n💡 推荐配置: {best_config['name']}")
            print(f"   理由: 在 {best_config['processing_time']:.3f}s 内实现了 {best_config['change_percentage']:.1f}% 的变化")

if __name__ == "__main__":
    main()
