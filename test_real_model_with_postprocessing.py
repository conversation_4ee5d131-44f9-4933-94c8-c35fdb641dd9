#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实模型后处理测试脚本
测试后处理在实际最佳模型上的效果
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
import os
import glob
import logging
from datetime import datetime
import json
import time
import sys
from typing import Dict, List, Tuple, Optional

# 添加路径
sys.path.append('UNet_Demo/UNet_Demo')

# 导入后处理模块
from enhanced_postprocessing_fixed import create_postprocessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealModelPostProcessingTester:
    """真实模型后处理测试器"""
    
    def __init__(self, model_path: str = "best_smart_optimized_miou_0.4477.pth", num_classes: int = 29):
        self.model_path = model_path
        self.num_classes = num_classes
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = self._load_model()
        
        # 创建后处理器
        self.postprocessors = {
            'fast': create_postprocessor(preset='fast'),
            'balanced': create_postprocessor(preset='balanced'),
            'high_quality': create_postprocessor(preset='high_quality')
        }
        
        # 结果存储
        self.results = {}
        
    def _load_model(self):
        """加载真实模型"""
        try:
            from unet import Unet
            
            # 创建模型实例
            model = Unet(num_classes=self.num_classes, backbone="resnet50")
            
            if os.path.exists(self.model_path):
                # 加载权重
                checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
                
                if 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                    logger.info(f"从checkpoint加载模型权重")
                else:
                    model.load_state_dict(checkpoint)
                    logger.info(f"直接加载模型权重")
                    
                logger.info(f"成功加载模型: {self.model_path}")
            else:
                logger.error(f"模型文件不存在: {self.model_path}")
                return None
                
            model.to(self.device)
            model.eval()
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return None
    
    def _load_test_data(self, data_dir: str = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007", 
                       num_samples: int = 20) -> List[Tuple[np.ndarray, np.ndarray, str]]:
        """加载测试数据"""
        image_dir = os.path.join(data_dir, "JPEGImages")
        label_dir = os.path.join(data_dir, "SegmentationClass")
        
        # 获取图像文件列表
        image_files = glob.glob(os.path.join(image_dir, "*.jpg"))
        if not image_files:
            image_files = glob.glob(os.path.join(image_dir, "*.png"))
            
        if len(image_files) == 0:
            logger.error(f"在 {image_dir} 中未找到图像文件")
            return []
        
        # 限制样本数量
        image_files = image_files[:num_samples]
        test_data = []
        
        for image_path in image_files:
            try:
                # 加载图像
                image = cv2.imread(image_path)
                if image is None:
                    continue
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                # 查找对应的标签
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                label_path = os.path.join(label_dir, base_name + ".png")
                
                if os.path.exists(label_path):
                    label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
                    if label is not None:
                        test_data.append((image, label, base_name))
                        
            except Exception as e:
                logger.warning(f"加载数据失败 {image_path}: {e}")
                continue
        
        logger.info(f"成功加载 {len(test_data)} 个测试样本")
        return test_data
    
    def _preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """图像预处理"""
        # 调整尺寸到模型输入尺寸
        image_resized = cv2.resize(image, (512, 512))
        
        # 归一化
        image_normalized = image_resized.astype(np.float32) / 255.0
        
        # 转换为tensor
        image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
        image_tensor = image_tensor.to(self.device)
        
        return image_tensor
    
    def _calculate_miou(self, pred: np.ndarray, target: np.ndarray) -> float:
        """计算mIoU"""
        # 确保尺寸匹配
        if pred.shape != target.shape:
            pred = cv2.resize(pred.astype(np.uint8), (target.shape[1], target.shape[0]), 
                            interpolation=cv2.INTER_NEAREST)
        
        miou = 0
        valid_classes = 0
        
        for class_id in range(self.num_classes):
            pred_mask = (pred == class_id)
            target_mask = (target == class_id)
            
            intersection = np.logical_and(pred_mask, target_mask).sum()
            union = np.logical_or(pred_mask, target_mask).sum()
            
            if union > 0:
                iou = intersection / union
                miou += iou
                valid_classes += 1
        
        return miou / valid_classes if valid_classes > 0 else 0
    
    def test_postprocessing_on_real_model(self, num_samples: int = 20) -> Dict:
        """在真实模型上测试后处理效果"""
        if self.model is None:
            logger.error("模型未加载，无法进行测试")
            return {}
        
        logger.info(f"🚀 开始在真实模型上测试后处理效果")
        logger.info(f"📊 模型: {self.model_path}")
        logger.info(f"🔢 测试样本数: {num_samples}")
        
        # 加载测试数据
        test_data = self._load_test_data(num_samples=num_samples)
        if not test_data:
            logger.error("无法加载测试数据")
            return {}
        
        results = {
            'baseline': {'miou_scores': [], 'processing_times': []},
            'fast': {'miou_scores': [], 'processing_times': []},
            'balanced': {'miou_scores': [], 'processing_times': []},
            'high_quality': {'miou_scores': [], 'processing_times': []}
        }
        
        with torch.no_grad():
            for i, (image, label, name) in enumerate(test_data):
                logger.info(f"处理样本 {i+1}/{len(test_data)}: {name}")
                
                # 预处理图像
                image_tensor = self._preprocess_image(image)
                
                # 模型推理
                start_time = time.time()
                logits = self.model(image_tensor)
                inference_time = time.time() - start_time
                
                # 基线预测（无后处理）
                baseline_pred = torch.argmax(logits, dim=1)[0].cpu().numpy()
                baseline_miou = self._calculate_miou(baseline_pred, label)
                
                results['baseline']['miou_scores'].append(baseline_miou)
                results['baseline']['processing_times'].append(inference_time)
                
                logger.info(f"  基线mIoU: {baseline_miou:.4f}")
                
                # 测试不同后处理配置
                for config_name, processor in self.postprocessors.items():
                    start_time = time.time()
                    
                    try:
                        # 后处理
                        processed_pred, confidence_map, processing_info = processor.process_prediction(
                            logits[0],  # 移除batch维度
                            original_image=image,
                            original_size=label.shape
                        )
                        
                        processing_time = time.time() - start_time + inference_time  # 包含推理时间
                        processed_miou = self._calculate_miou(processed_pred, label)
                        
                        results[config_name]['miou_scores'].append(processed_miou)
                        results[config_name]['processing_times'].append(processing_time)
                        
                        improvement = processed_miou - baseline_miou
                        logger.info(f"  {config_name}: mIoU={processed_miou:.4f} (+{improvement:+.4f})")
                        
                    except Exception as e:
                        logger.warning(f"  {config_name} 处理失败: {e}")
                        results[config_name]['miou_scores'].append(baseline_miou)
                        results[config_name]['processing_times'].append(inference_time)
                
                if (i + 1) % 5 == 0:
                    logger.info(f"已完成 {i+1}/{len(test_data)} 个样本")
        
        # 计算统计信息
        summary = {}
        for config_name, data in results.items():
            miou_scores = data['miou_scores']
            processing_times = data['processing_times']
            
            if miou_scores:
                summary[config_name] = {
                    'avg_miou': np.mean(miou_scores),
                    'std_miou': np.std(miou_scores),
                    'min_miou': np.min(miou_scores),
                    'max_miou': np.max(miou_scores),
                    'avg_processing_time': np.mean(processing_times),
                    'std_processing_time': np.std(processing_times),
                    'samples': len(miou_scores)
                }
                
                if config_name != 'baseline':
                    baseline_avg = summary.get('baseline', {}).get('avg_miou', 0)
                    if baseline_avg > 0:
                        summary[config_name]['miou_improvement'] = summary[config_name]['avg_miou'] - baseline_avg
                        summary[config_name]['improvement_percentage'] = (summary[config_name]['miou_improvement'] / baseline_avg) * 100
        
        return summary
    
    def generate_report(self, results: Dict, output_path: str = None) -> str:
        """生成详细报告"""
        if output_path is None:
            output_path = f"real_model_postprocessing_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 保存完整结果
        report_data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'model_path': self.model_path,
            'test_type': 'real_model_postprocessing_evaluation',
            'device': str(self.device),
            'results': results
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 生成文本报告
        text_report = self._generate_text_report(results)
        
        text_path = output_path.replace('.json', '_report.md')
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        logger.info(f"报告已保存: {output_path}")
        logger.info(f"文本报告: {text_path}")
        
        return text_report
    
    def _generate_text_report(self, results: Dict) -> str:
        """生成文本报告"""
        if not results or 'baseline' not in results:
            return "# 错误：无有效结果数据"
        
        baseline_miou = results['baseline']['avg_miou']
        
        report = f"""# 🔍 真实模型后处理效果测试报告

## 📊 测试配置

- **模型**: {self.model_path}
- **设备**: {self.device}
- **类别数**: {self.num_classes}
- **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📈 性能对比

### 基线性能 (无后处理)
- **平均mIoU**: {baseline_miou:.4f}
- **标准差**: ±{results['baseline']['std_miou']:.4f}
- **范围**: {results['baseline']['min_miou']:.4f} - {results['baseline']['max_miou']:.4f}
- **平均推理时间**: {results['baseline']['avg_processing_time']:.3f}s

"""
        
        # 后处理配置对比
        configs = ['fast', 'balanced', 'high_quality']
        best_config = 'baseline'
        best_miou = baseline_miou
        
        for config in configs:
            if config in results:
                data = results[config]
                improvement = data.get('miou_improvement', 0)
                improvement_pct = data.get('improvement_percentage', 0)
                
                report += f"""### {config.upper()} 配置
- **平均mIoU**: {data['avg_miou']:.4f}
- **mIoU提升**: {improvement:+.4f} ({improvement_pct:+.1f}%)
- **标准差**: ±{data['std_miou']:.4f}
- **范围**: {data['min_miou']:.4f} - {data['max_miou']:.4f}
- **平均处理时间**: {data['avg_processing_time']:.3f}s

"""
                
                if data['avg_miou'] > best_miou:
                    best_miou = data['avg_miou']
                    best_config = config
        
        # 推荐配置
        total_improvement = best_miou - baseline_miou
        total_improvement_pct = (total_improvement / baseline_miou * 100) if baseline_miou > 0 else 0
        
        report += f"""## 🏆 最佳配置

**推荐配置**: {best_config.upper()}
- **最高mIoU**: {best_miou:.4f}
- **总提升**: {total_improvement:+.4f} ({total_improvement_pct:+.1f}%)

## 💡 结论

"""
        
        if total_improvement > 0.01:  # 提升超过1%
            report += f"""✅ **后处理显著提升了模型性能！**

- 推荐在生产环境中使用 **{best_config}** 配置
- 预期可将mIoU从 {baseline_miou:.4f} 提升到 {best_miou:.4f}
- 这将使模型更接近论文级别的性能标准

"""
        else:
            report += f"""⚠️ **后处理提升有限**

可能的原因：
1. 当前模型已经具有较好的预测质量
2. 后处理参数需要进一步调优
3. 数据集特性不适合当前后处理策略

"""
        
        # 下一步建议
        if baseline_miou >= 0.4:
            if total_improvement > 0.005:
                report += f"""## 🎯 下一步建议

1. **立即应用**: 将 {best_config} 后处理集成到生产流程
2. **参数优化**: 针对当前数据集微调后处理参数
3. **架构升级**: 考虑升级到UNet++等更强架构
4. **论文发表**: 当前性能已接近论文级别标准

"""
            else:
                report += f"""## 🎯 下一步建议

1. **架构优先**: 专注于UNet++等架构升级
2. **损失函数**: 尝试Lovász Loss等先进损失函数
3. **数据增强**: 加强数据增强策略
4. **模型集成**: 考虑多模型集成方案

"""
        else:
            report += f"""## 🎯 下一步建议

1. **基础优化**: 继续优化基础模型性能
2. **训练策略**: 改进训练策略和超参数
3. **数据质量**: 检查数据质量和标注准确性
4. **后处理**: 在基础性能提升后再考虑后处理

"""
        
        report += f"""---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return report

def main():
    """主函数"""
    print("🚀 真实模型后处理效果测试")
    print("=" * 60)
    
    # 检查模型文件
    model_path = "best_smart_optimized_miou_0.4477.pth"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📁 模型文件: {model_path}")
    
    # 创建测试器
    tester = RealModelPostProcessingTester(model_path=model_path)
    
    if tester.model is None:
        print("❌ 模型加载失败")
        return
    
    print("✅ 模型加载成功")
    
    # 运行测试
    print("\n🧪 开始测试...")
    results = tester.test_postprocessing_on_real_model(num_samples=15)  # 使用较少样本以节省时间
    
    if not results:
        print("❌ 测试失败")
        return
    
    # 生成报告
    report = tester.generate_report(results)
    
    # 显示关键结果
    print("\n" + "="*60)
    print("📊 测试结果摘要")
    print("="*60)
    
    if 'baseline' in results:
        baseline_miou = results['baseline']['avg_miou']
        print(f"📈 基线mIoU: {baseline_miou:.4f}")
        
        best_improvement = 0
        best_config = 'baseline'
        
        for config in ['fast', 'balanced', 'high_quality']:
            if config in results:
                improvement = results[config].get('miou_improvement', 0)
                miou = results[config]['avg_miou']
                time_cost = results[config]['avg_processing_time']
                
                print(f"🔧 {config}: mIoU={miou:.4f} (+{improvement:+.4f}) 时间={time_cost:.3f}s")
                
                if improvement > best_improvement:
                    best_improvement = improvement
                    best_config = config
        
        print(f"\n🏆 最佳配置: {best_config}")
        print(f"📊 最大提升: +{best_improvement:.4f} mIoU")
        
        if best_improvement > 0.005:
            final_miou = baseline_miou + best_improvement
            print(f"🎯 预期最终mIoU: {final_miou:.4f}")
            
            if final_miou >= 0.5:
                print("🎉 恭喜！已达到论文级别性能 (≥0.5)！")
            else:
                remaining = 0.5 - final_miou
                print(f"📈 距离论文级别还需: +{remaining:.4f}")
        else:
            print("⚠️ 后处理提升有限，建议优先考虑架构升级")

if __name__ == "__main__":
    main()
