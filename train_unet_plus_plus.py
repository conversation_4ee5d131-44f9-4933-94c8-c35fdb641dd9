#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import logging
from datetime import datetime
import json
import time
from typing import Dict, List, Any, Optional

# 导入我们的模块
from unet_plus_plus import create_unet_plus_plus, UNetPlusPlusLoss
from enhanced_postprocessing import EnhancedPostProcessor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UNetPlusPlusTrainer:
    """UNet++训练器"""

    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 解析配置
        basic_config = config.get('basic', {})
        model_config = config.get('model', {})

        # 创建模型
        self.model = create_unet_plus_plus(
            num_classes=basic_config.get('num_classes', 29),
            backbone=model_config.get('backbone', 'resnet50'),
            pretrained=model_config.get('pretrained', True),
            deep_supervision=model_config.get('deep_supervision', True),
            use_attention=model_config.get('use_attention', True),
            use_cbam=model_config.get('use_cbam', True)
        ).to(self.device)

        # 创建损失函数
        loss_config = config.get('loss', {})
        self.criterion = UNetPlusPlusLoss(
            num_classes=basic_config.get('num_classes', 29),
            deep_supervision=model_config.get('deep_supervision', True),
            weights=loss_config.get('class_weights')
        ).to(self.device)

        # 创建优化器
        self.optimizer = self._create_optimizer()

        # 创建学习率调度器
        self.scheduler = self._create_scheduler()

        # 创建后处理器
        self.postprocessor = EnhancedPostProcessor(
            num_classes=config['num_classes'],
            device=self.device
        )

        # 训练状态
        self.current_epoch = 0
        self.best_miou = 0.0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'val_miou': [],
            'val_miou_postprocessed': [],
            'learning_rates': []
        }

        # 创建保存目录
        self.save_dir = f"logs/unet_plus_plus_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.save_dir, exist_ok=True)

        # 保存配置
        with open(os.path.join(self.save_dir, 'config.json'), 'w') as f:
            json.dump(config, f, indent=2)

    def _create_optimizer(self):
        """创建优化器"""
        training_config = self.config.get('training', {})
        optimizer_config = training_config.get('optimizer', {})

        optimizer_type = optimizer_config.get('type', 'AdamW')
        lr = optimizer_config.get('lr', 2e-4)
        weight_decay = optimizer_config.get('weight_decay', 1e-4)

        if optimizer_type == 'Adam':
            return optim.Adam(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        elif optimizer_type == 'AdamW':
            return optim.AdamW(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=optimizer_config.get('betas', [0.9, 0.999]),
                eps=optimizer_config.get('eps', 1e-8)
            )
        elif optimizer_type == 'SGD':
            return optim.SGD(
                self.model.parameters(),
                lr=lr,
                momentum=0.9,
                weight_decay=weight_decay
            )
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_type}")

    def _create_scheduler(self):
        """创建学习率调度器"""
        training_config = self.config.get('training', {})
        scheduler_config = training_config.get('scheduler', {})

        scheduler_type = scheduler_config.get('type', 'CosineAnnealingWarmRestarts')

        if scheduler_type == 'CosineAnnealingLR':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=training_config.get('epochs', 150),
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
        elif scheduler_type == 'CosineAnnealingWarmRestarts':
            return optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=scheduler_config.get('T_0', 30),
                T_mult=scheduler_config.get('T_mult', 2),
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
        elif scheduler_type == 'ReduceLROnPlateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=0.5,
                patience=10,
                verbose=True
            )
        elif scheduler_type == 'StepLR':
            return optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=30,
                gamma=0.1
            )
        else:
            return None

    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = len(train_loader)

        for batch_idx, (images, targets) in enumerate(train_loader):
            images = images.to(self.device)
            targets = targets.to(self.device)

            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(images)

            # 计算损失
            loss = self.criterion(outputs, targets)

            # 反向传播
            loss.backward()

            # 梯度裁剪
            if self.config.get('gradient_clipping'):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['gradient_clipping']
                )

            self.optimizer.step()

            total_loss += loss.item()

            # 打印进度
            if batch_idx % 10 == 0:
                logger.info(f'Epoch {self.current_epoch}, Batch {batch_idx}/{num_batches}, '
                          f'Loss: {loss.item():.4f}')

        return total_loss / num_batches

    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        total_miou = 0
        total_miou_postprocessed = 0
        num_batches = len(val_loader)

        with torch.no_grad():
            for batch_idx, (images, targets) in enumerate(val_loader):
                images = images.to(self.device)
                targets = targets.to(self.device)

                # 前向传播
                outputs = self.model(images)

                # 计算损失
                loss = self.criterion(outputs, targets)
                total_loss += loss.item()

                # 计算mIoU
                if isinstance(outputs, list):
                    # 深度监督：使用最后一个输出
                    pred_logits = outputs[-1]
                else:
                    pred_logits = outputs

                # 原始预测的mIoU
                pred = torch.argmax(pred_logits, dim=1)
                miou = self._calculate_miou(pred, targets)
                total_miou += miou

                # 后处理预测的mIoU
                if batch_idx < 10:  # 只对部分样本进行后处理（节省时间）
                    try:
                        # 对第一个样本进行后处理
                        processed_pred, _, _ = self.postprocessor.process_prediction(
                            pred_logits[0:1],
                            original_image=None,  # 这里可以传入原始图像
                            original_size=None
                        )
                        miou_postprocessed = self._calculate_miou(
                            torch.from_numpy(processed_pred).unsqueeze(0).to(self.device),
                            targets[0:1]
                        )
                        total_miou_postprocessed += miou_postprocessed
                    except Exception as e:
                        logger.warning(f"后处理失败: {e}")
                        total_miou_postprocessed += miou
                else:
                    total_miou_postprocessed += miou

        avg_loss = total_loss / num_batches
        avg_miou = total_miou / num_batches
        avg_miou_postprocessed = total_miou_postprocessed / num_batches

        return avg_loss, avg_miou, avg_miou_postprocessed

    def _calculate_miou(self, pred, target):
        """计算mIoU"""
        pred = pred.cpu().numpy()
        target = target.cpu().numpy()

        miou = 0
        valid_classes = 0

        for class_id in range(self.config['num_classes']):
            pred_mask = (pred == class_id)
            target_mask = (target == class_id)

            intersection = np.logical_and(pred_mask, target_mask).sum()
            union = np.logical_or(pred_mask, target_mask).sum()

            if union > 0:
                iou = intersection / union
                miou += iou
                valid_classes += 1

        return miou / valid_classes if valid_classes > 0 else 0

    def save_checkpoint(self, epoch, miou, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_miou': self.best_miou,
            'miou': miou,
            'config': self.config,
            'training_history': self.training_history
        }

        # 保存最新检查点
        checkpoint_path = os.path.join(self.save_dir, 'latest_checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)

        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.save_dir, f'best_unet_plus_plus_miou_{miou:.4f}.pth')
            torch.save(checkpoint, best_path)
            logger.info(f"保存最佳模型: {best_path}")

    def train(self, train_loader, val_loader):
        """完整训练流程"""
        logger.info("开始UNet++训练...")
        logger.info(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")

        for epoch in range(self.config['epochs']):
            self.current_epoch = epoch
            start_time = time.time()

            # 训练
            train_loss = self.train_epoch(train_loader)

            # 验证
            val_loss, val_miou, val_miou_postprocessed = self.validate_epoch(val_loader)

            # 更新学习率
            if self.scheduler:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_miou)
                else:
                    self.scheduler.step()

            # 记录历史
            current_lr = self.optimizer.param_groups[0]['lr']
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_miou'].append(val_miou)
            self.training_history['val_miou_postprocessed'].append(val_miou_postprocessed)
            self.training_history['learning_rates'].append(current_lr)

            # 检查是否是最佳模型
            is_best = val_miou_postprocessed > self.best_miou
            if is_best:
                self.best_miou = val_miou_postprocessed

            # 保存检查点
            self.save_checkpoint(epoch, val_miou_postprocessed, is_best)

            # 打印统计信息
            epoch_time = time.time() - start_time
            logger.info(f'Epoch {epoch+1}/{self.config["epochs"]} - '
                       f'Time: {epoch_time:.2f}s - '
                       f'Train Loss: {train_loss:.4f} - '
                       f'Val Loss: {val_loss:.4f} - '
                       f'Val mIoU: {val_miou:.4f} - '
                       f'Val mIoU (后处理): {val_miou_postprocessed:.4f} - '
                       f'LR: {current_lr:.6f}')

            # 早停检查
            if self.config.get('early_stopping'):
                patience = self.config['early_stopping']['patience']
                if epoch > patience:
                    recent_miou = self.training_history['val_miou_postprocessed'][-patience:]
                    if all(miou <= self.best_miou for miou in recent_miou):
                        logger.info(f"早停触发，在epoch {epoch+1}")
                        break

        logger.info(f"训练完成！最佳mIoU: {self.best_miou:.4f}")
        return self.best_miou

def load_config(config_path: str = 'config_unet_plus_plus.yaml') -> Dict:
    """加载配置文件"""
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"成功加载配置文件: {config_path}")
        return config
    except FileNotFoundError:
        logger.error(f"配置文件未找到: {config_path}")
        return create_default_config()
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return create_default_config()

def create_default_config():
    """创建默认配置"""
    return {
        'basic': {'num_classes': 29, 'device': 'cuda'},
        'model': {
            'architecture': 'unet_plus_plus',
            'backbone': 'resnet50',
            'pretrained': True,
            'deep_supervision': True,
            'use_attention': True,
            'use_cbam': True
        },
        'training': {
            'epochs': 150,
            'batch_size': 8,
            'optimizer': {'type': 'AdamW', 'lr': 2e-4, 'weight_decay': 1e-4},
            'scheduler': {'type': 'CosineAnnealingWarmRestarts', 'T_0': 30},
            'mixed_precision': True,
            'gradient_clipping': 1.0
        },
        'loss': {
            'deep_supervision_weights': [0.1, 0.2, 0.3, 0.4],
            'primary_loss': {
                'type': 'combined',
                'components': {
                    'cross_entropy': {'weight': 0.4},
                    'focal_loss': {'weight': 0.3, 'alpha': 0.25, 'gamma': 2.0},
                    'dice_loss': {'weight': 0.3, 'smooth': 1.0}
                }
            }
        },
        'validation': {'frequency': 1, 'postprocessing': {'enabled': True, 'config': 'balanced'}},
        'early_stopping': {'enabled': True, 'patience': 25}
    }

def test_unet_plus_plus():
    """测试UNet++模型"""
    print("🧪 测试UNet++模型")

    try:
        # 创建模型
        model = create_unet_plus_plus(
            num_classes=29,
            backbone='resnet50',
            deep_supervision=True,
            use_attention=True,
            use_cbam=True
        )

        # 测试前向传播
        x = torch.randn(2, 3, 512, 512)

        with torch.no_grad():
            outputs = model(x)

        if isinstance(outputs, list):
            print(f"✅ 深度监督输出数量: {len(outputs)}")
            for i, output in enumerate(outputs):
                print(f"   输出 {i+1} 形状: {output.shape}")
        else:
            print(f"✅ 输出形状: {outputs.shape}")

        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        print(f"📊 模型统计:")
        print(f"   总参数量: {total_params:,}")
        print(f"   可训练参数量: {trainable_params:,}")
        print(f"   模型大小: {total_params * 4 / 1024 / 1024:.1f} MB")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 UNet++ Phase 2 - 架构升级")
    print("=" * 60)

    # 测试模型
    if test_unet_plus_plus():
        print("\n✅ UNet++模型测试通过")
    else:
        print("\n❌ UNet++模型测试失败")
        return

    # 加载配置
    config = load_config()

    print(f"\n📋 配置摘要:")
    print(f"   架构: {config.get('model', {}).get('architecture', 'unet_plus_plus')}")
    print(f"   骨干网络: {config.get('model', {}).get('backbone', 'resnet50')}")
    print(f"   深度监督: {config.get('model', {}).get('deep_supervision', True)}")
    print(f"   注意力机制: {config.get('model', {}).get('use_attention', True)}")
    print(f"   CBAM: {config.get('model', {}).get('use_cbam', True)}")

    # 创建训练器
    try:
        trainer = UNetPlusPlusTrainer(config)
        print(f"\n✅ 训练器创建成功")
        print(f"📁 保存目录: {trainer.save_dir}")

        print(f"\n🎯 下一步:")
        print(f"   1. 准备数据加载器")
        print(f"   2. 开始训练: trainer.train(train_loader, val_loader)")
        print(f"   3. 监控训练进度")

    except Exception as e:
        print(f"\n❌ 训练器创建失败: {e}")

if __name__ == "__main__":
    main()
