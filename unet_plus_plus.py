#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional
import torchvision.models as models

class ConvBlock(nn.Module):
    """基础卷积块"""

    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1, use_batchnorm=True):
        super(ConvBlock, self).__init__()

        layers = []
        layers.append(nn.Conv2d(in_channels, out_channels, kernel_size, padding=padding))

        if use_batchnorm:
            layers.append(nn.BatchNorm2d(out_channels))

        layers.append(nn.ReLU(inplace=True))
        layers.append(nn.Conv2d(out_channels, out_channels, kernel_size, padding=padding))

        if use_batchnorm:
            layers.append(nn.BatchNorm2d(out_channels))

        layers.append(nn.ReLU(inplace=True))

        self.conv = nn.Sequential(*layers)

    def forward(self, x):
        return self.conv(x)

class AttentionBlock(nn.Module):
    """注意力机制块"""

    def __init__(self, F_g, F_l, F_int):
        super(AttentionBlock, self).__init__()

        self.W_g = nn.Sequential(
            nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )

        self.W_x = nn.Sequential(
            nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )

        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(1),
            nn.Sigmoid()
        )

        self.relu = nn.ReLU(inplace=True)

    def forward(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        return x * psi

class CBAM(nn.Module):
    """CBAM注意力机制"""

    def __init__(self, channels, reduction=16):
        super(CBAM, self).__init__()

        # Channel attention
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )

        # Spatial attention
        self.conv = nn.Conv2d(2, 1, 7, padding=3, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # Channel attention
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        channel_att = self.sigmoid(avg_out + max_out)
        x = x * channel_att

        # Spatial attention
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial_att = self.sigmoid(self.conv(torch.cat([avg_out, max_out], dim=1)))
        x = x * spatial_att

        return x

class UNetPlusPlus(nn.Module):
    """UNet++ 架构实现"""

    def __init__(self, num_classes=29, backbone='resnet50', pretrained=True,
                 deep_supervision=True, use_attention=True, use_cbam=True):
        super(UNetPlusPlus, self).__init__()

        self.num_classes = num_classes
        self.deep_supervision = deep_supervision
        self.use_attention = use_attention
        self.use_cbam = use_cbam

        # 编码器 (Backbone)
        self.encoder = self._get_encoder(backbone, pretrained)

        # 获取编码器特征通道数
        self.encoder_channels = self._get_encoder_channels(backbone)

        # 解码器通道数 - 与编码器对应
        self.decoder_channels = [64, 128, 256, 512, 1024]  # 从小到大

        # 构建UNet++的嵌套结构
        self._build_nested_unet()

        # 最终分类头
        self._build_classification_heads()

    def _get_encoder(self, backbone, pretrained):
        """获取编码器"""
        if backbone == 'resnet50':
            encoder = models.resnet50(pretrained=pretrained)
            return nn.ModuleList([
                nn.Sequential(encoder.conv1, encoder.bn1, encoder.relu),
                nn.Sequential(encoder.maxpool, encoder.layer1),
                encoder.layer2,
                encoder.layer3,
                encoder.layer4
            ])
        elif backbone == 'resnet101':
            encoder = models.resnet101(pretrained=pretrained)
            return nn.ModuleList([
                nn.Sequential(encoder.conv1, encoder.bn1, encoder.relu),
                nn.Sequential(encoder.maxpool, encoder.layer1),
                encoder.layer2,
                encoder.layer3,
                encoder.layer4
            ])
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")

    def _get_encoder_channels(self, backbone):
        """获取编码器各层的通道数"""
        if backbone in ['resnet50', 'resnet101']:
            return [64, 256, 512, 1024, 2048]
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")

    def _build_nested_unet(self):
        """构建UNet++的嵌套结构"""
        # 简化的UNet++结构，避免通道数不匹配问题

        # 编码器特征适配层
        self.adapt0 = nn.Conv2d(self.encoder_channels[0], self.decoder_channels[0], 1)
        self.adapt1 = nn.Conv2d(self.encoder_channels[1], self.decoder_channels[1], 1)
        self.adapt2 = nn.Conv2d(self.encoder_channels[2], self.decoder_channels[2], 1)
        self.adapt3 = nn.Conv2d(self.encoder_channels[3], self.decoder_channels[3], 1)
        self.adapt4 = nn.Conv2d(self.encoder_channels[4], self.decoder_channels[4], 1)

        # 第0层 (编码器输出直接适配)
        self.conv0_0 = ConvBlock(self.decoder_channels[0], self.decoder_channels[0])
        self.conv1_0 = ConvBlock(self.decoder_channels[1], self.decoder_channels[1])
        self.conv2_0 = ConvBlock(self.decoder_channels[2], self.decoder_channels[2])
        self.conv3_0 = ConvBlock(self.decoder_channels[3], self.decoder_channels[3])
        self.conv4_0 = ConvBlock(self.decoder_channels[4], self.decoder_channels[4])

        # 第1层 (第一次上采样和融合)
        self.conv0_1 = ConvBlock(self.decoder_channels[0] + self.decoder_channels[1], self.decoder_channels[0])
        self.conv1_1 = ConvBlock(self.decoder_channels[1] + self.decoder_channels[2], self.decoder_channels[1])
        self.conv2_1 = ConvBlock(self.decoder_channels[2] + self.decoder_channels[3], self.decoder_channels[2])
        self.conv3_1 = ConvBlock(self.decoder_channels[3] + self.decoder_channels[4], self.decoder_channels[3])

        # 第2层
        self.conv0_2 = ConvBlock(self.decoder_channels[0] * 2 + self.decoder_channels[1], self.decoder_channels[0])
        self.conv1_2 = ConvBlock(self.decoder_channels[1] * 2 + self.decoder_channels[2], self.decoder_channels[1])
        self.conv2_2 = ConvBlock(self.decoder_channels[2] * 2 + self.decoder_channels[3], self.decoder_channels[2])

        # 第3层
        self.conv0_3 = ConvBlock(self.decoder_channels[0] * 3 + self.decoder_channels[1], self.decoder_channels[0])
        self.conv1_3 = ConvBlock(self.decoder_channels[1] * 3 + self.decoder_channels[2], self.decoder_channels[1])

        # 第4层
        self.conv0_4 = ConvBlock(self.decoder_channels[0] * 4 + self.decoder_channels[1], self.decoder_channels[0])

        # 上采样层
        self.up1 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.up2 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.up3 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.up4 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)

        # CBAM注意力
        if self.use_cbam:
            self.cbam0 = CBAM(self.decoder_channels[0])
            self.cbam1 = CBAM(self.decoder_channels[1])
            self.cbam2 = CBAM(self.decoder_channels[2])
            self.cbam3 = CBAM(self.decoder_channels[3])
            self.cbam4 = CBAM(self.decoder_channels[4])

    def _build_classification_heads(self):
        """构建分类头"""
        if self.deep_supervision:
            # 深度监督：多个输出
            self.final1 = nn.Conv2d(self.decoder_channels[0], self.num_classes, kernel_size=1)
            self.final2 = nn.Conv2d(self.decoder_channels[0], self.num_classes, kernel_size=1)
            self.final3 = nn.Conv2d(self.decoder_channels[0], self.num_classes, kernel_size=1)
            self.final4 = nn.Conv2d(self.decoder_channels[0], self.num_classes, kernel_size=1)
        else:
            # 单一输出
            self.final = nn.Conv2d(self.decoder_channels[0], self.num_classes, kernel_size=1)

    def forward(self, x):
        # 编码器前向传播
        enc0 = self.encoder[0](x)  # 1/2
        enc1 = self.encoder[1](enc0)  # 1/4
        enc2 = self.encoder[2](enc1)  # 1/8
        enc3 = self.encoder[3](enc2)  # 1/16
        enc4 = self.encoder[4](enc3)  # 1/32

        # 特征适配
        x0_0 = self.adapt0(enc0)
        x1_0 = self.adapt1(enc1)
        x2_0 = self.adapt2(enc2)
        x3_0 = self.adapt3(enc3)
        x4_0 = self.adapt4(enc4)

        # 应用CBAM注意力
        if self.use_cbam:
            x0_0 = self.cbam0(x0_0)
            x1_0 = self.cbam1(x1_0)
            x2_0 = self.cbam2(x2_0)
            x3_0 = self.cbam3(x3_0)
            x4_0 = self.cbam4(x4_0)

        # 解码器特征处理
        x0_0 = self.conv0_0(x0_0)
        x1_0 = self.conv1_0(x1_0)
        x2_0 = self.conv2_0(x2_0)
        x3_0 = self.conv3_0(x3_0)
        x4_0 = self.conv4_0(x4_0)

        # 第1层解码
        x3_1 = self.conv3_1(torch.cat([x3_0, self.up4(x4_0)], 1))
        x2_1 = self.conv2_1(torch.cat([x2_0, self.up3(x3_1)], 1))
        x1_1 = self.conv1_1(torch.cat([x1_0, self.up2(x2_1)], 1))
        x0_1 = self.conv0_1(torch.cat([x0_0, self.up1(x1_1)], 1))

        # 第2层解码
        x2_2 = self.conv2_2(torch.cat([x2_0, x2_1, self.up3(x3_1)], 1))
        x1_2 = self.conv1_2(torch.cat([x1_0, x1_1, self.up2(x2_2)], 1))
        x0_2 = self.conv0_2(torch.cat([x0_0, x0_1, self.up1(x1_2)], 1))

        # 第3层解码
        x1_3 = self.conv1_3(torch.cat([x1_0, x1_1, x1_2, self.up2(x2_2)], 1))
        x0_3 = self.conv0_3(torch.cat([x0_0, x0_1, x0_2, self.up1(x1_3)], 1))

        # 第4层解码
        x0_4 = self.conv0_4(torch.cat([x0_0, x0_1, x0_2, x0_3, self.up1(x1_3)], 1))

        # 最终输出
        if self.deep_supervision:
            # 深度监督输出
            output1 = self.final1(x0_1)
            output2 = self.final2(x0_2)
            output3 = self.final3(x0_3)
            output4 = self.final4(x0_4)

            # 上采样到原始尺寸
            output1 = F.interpolate(output1, size=x.shape[2:], mode='bilinear', align_corners=True)
            output2 = F.interpolate(output2, size=x.shape[2:], mode='bilinear', align_corners=True)
            output3 = F.interpolate(output3, size=x.shape[2:], mode='bilinear', align_corners=True)
            output4 = F.interpolate(output4, size=x.shape[2:], mode='bilinear', align_corners=True)

            return [output1, output2, output3, output4]
        else:
            # 单一输出
            output = self.final(x0_4)
            output = F.interpolate(output, size=x.shape[2:], mode='bilinear', align_corners=True)
            return output

class UNetPlusPlusLoss(nn.Module):
    """UNet++专用损失函数"""

    def __init__(self, num_classes=29, deep_supervision=True, weights=None):
        super(UNetPlusPlusLoss, self).__init__()
        self.deep_supervision = deep_supervision
        self.num_classes = num_classes

        # 损失函数权重
        if weights is not None:
            self.ce_loss = nn.CrossEntropyLoss(weight=torch.FloatTensor(weights))
        else:
            self.ce_loss = nn.CrossEntropyLoss()

        # 深度监督的权重
        self.ds_weights = [0.1, 0.2, 0.3, 0.4] if deep_supervision else [1.0]

    def forward(self, outputs, targets):
        if self.deep_supervision and isinstance(outputs, list):
            total_loss = 0
            for i, output in enumerate(outputs):
                loss = self.ce_loss(output, targets)
                total_loss += self.ds_weights[i] * loss
            return total_loss
        else:
            return self.ce_loss(outputs, targets)

def create_unet_plus_plus(num_classes=29, backbone='resnet50', pretrained=True,
                         deep_supervision=True, use_attention=True, use_cbam=True):
    """创建UNet++模型的工厂函数"""
    model = UNetPlusPlus(
        num_classes=num_classes,
        backbone=backbone,
        pretrained=pretrained,
        deep_supervision=deep_supervision,
        use_attention=use_attention,
        use_cbam=use_cbam
    )
    return model

if __name__ == "__main__":
    # 测试模型
    model = create_unet_plus_plus(num_classes=29, backbone='resnet50')

    # 测试输入
    x = torch.randn(2, 3, 512, 512)

    # 前向传播
    with torch.no_grad():
        outputs = model(x)

    if isinstance(outputs, list):
        print(f"深度监督输出数量: {len(outputs)}")
        for i, output in enumerate(outputs):
            print(f"输出 {i+1} 形状: {output.shape}")
    else:
        print(f"输出形状: {outputs.shape}")

    # 计算参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
