{"model_name": "unet_plus_plus", "num_classes": 29, "input_shape": [512, 512], "backbone": "resnet50", "batch_size": 4, "epochs": 100, "learning_rate": 0.001, "weight_decay": 0.0001, "momentum": 0.9, "optimizer": "adamw", "scheduler": "cosine", "warmup_epochs": 5, "mixed_precision": true, "loss_config": {"focal_weight": 0.4, "dice_weight": 0.3, "ce_weight": 0.3, "deep_supervision": true, "deep_supervision_weights": [0.5, 0.3, 0.15, 0.05]}, "augmentation": {"horizontal_flip": 0.5, "vertical_flip": 0.2, "rotation": 15, "brightness": 0.2, "contrast": 0.2, "saturation": 0.2, "hue": 0.1, "mixup_alpha": 0.2, "cutmix_alpha": 1.0}, "class_weights": {"strategy": "smart_balanced", "base_weights": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "rare_class_boost": 5.0, "common_class_penalty": 0.5}, "validation_frequency": 5, "save_frequency": 10, "early_stopping_patience": 20, "save_best_only": true, "postprocessing": {"enabled": true, "config": "balanced", "apply_during_validation": true}, "logging": {"log_frequency": 10, "tensorboard": true, "wandb": false, "save_predictions": true, "save_frequency": 20}, "paths": {"data_root": "UNet_Demo/UNet_Demo/VOCdevkit/VOC2007", "save_dir": "unet_plus_plus_training_20250527_143521", "pretrained_weights": null, "resume_from": null}}